'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface UseRealTimeAdvertiserApplicationsReturn {
  applications: Application[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const useRealTimeAdvertiserApplications = (
  pollingInterval: number = 30000 // 30 seconds default
): UseRealTimeAdvertiserApplicationsReturn => {
  const user = useSelector(selectUser);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const fetchApplications = useCallback(async () => {
    if (!user?.id || user.role !== 'advertiser') {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      const response = await fetch(`http://localhost:3000/applications`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Advertiser role required.');
        }
        throw new Error(`Failed to fetch applications: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform data to include mock relations (in real app, backend should provide this)
      const applicationsWithMockData = Array.isArray(data) ? data.map((app: any, index: number) => ({
        id: app.id || `app-${index}`,
        campaign: {
          id: app.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: user.id, // Associate with current advertiser
            email: user.email
          }
        },
        publisher: {
          id: app.publisherId || `pub-${index}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`
        },
        status: app.status === 'approved' ? 'sp_approved' : (app.status || 'pending'), // Map to workflow status
        feedback: app.feedback,
        createdAt: app.createdAt || new Date().toISOString(),
        updatedAt: app.updatedAt || new Date().toISOString(),
        spReviewer: app.spReviewerId ? {
          id: app.spReviewerId,
          email: `<EMAIL>`
        } : undefined,
        advertiserReviewer: app.advertiserReviewerId ? {
          id: app.advertiserReviewerId,
          email: user.email
        } : undefined
      })) : [];
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setApplications(applicationsWithMockData);
        setLastUpdated(new Date());
      }
    } catch (err) {
      if (mountedRef.current) {
        setError(err instanceof Error ? err.message : 'Failed to fetch applications');
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [user]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchApplications();
  }, [fetchApplications]);

  // Start polling
  const startPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }
    
    pollingRef.current = setInterval(() => {
      if (mountedRef.current) {
        fetchApplications();
      }
    }, pollingInterval);
  }, [fetchApplications, pollingInterval]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Initial fetch and setup polling
  useEffect(() => {
    mountedRef.current = true;
    
    if (user?.role === 'advertiser') {
      fetchApplications();
      startPolling();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
    };
  }, [user, fetchApplications, startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (user?.role === 'advertiser') {
        startPolling();
        // Fetch immediately when tab becomes visible
        fetchApplications();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, fetchApplications, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    applications,
    loading,
    error,
    refetch,
    lastUpdated
  };
};

export default useRealTimeAdvertiserApplications;
