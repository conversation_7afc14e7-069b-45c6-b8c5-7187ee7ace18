'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface UseRealTimeVideoReviewReturn {
  videoSubmissions: VideoSubmission[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const useRealTimeVideoReview = (
  userRole: 'staff' | 'advertiser',
  pollingInterval: number = 30000 // 30 seconds default
): UseRealTimeVideoReviewReturn => {
  const user = useSelector(selectUser);
  const [videoSubmissions, setVideoSubmissions] = useState<VideoSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const fetchVideoSubmissions = useCallback(async () => {
    if (!user?.id || (userRole === 'staff' && user.role !== 'staff') || (userRole === 'advertiser' && user.role !== 'advertiser')) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      // Build query parameters based on user role
      let queryParams = '';
      if (userRole === 'staff') {
        queryParams = '?status=pending,sp_review,advertiser_review,approved,rejected';
      } else if (userRole === 'advertiser') {
        queryParams = `?status=advertiser_review,approved,rejected,published&advertiserId=${user.id}`;
      }

      const response = await fetch(`http://localhost:3000/video-submission${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error(`Access denied. ${userRole === 'staff' ? 'SP Team' : 'Advertiser'} role required.`);
        }
        throw new Error(`Failed to fetch video submissions: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform data to include mock relations (in real app, backend should provide this)
      const submissionsWithMockData = Array.isArray(data) ? data.map((submission: any, index: number) => ({
        id: submission.id || `sub-${index}`,
        campaign: {
          id: submission.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: userRole === 'advertiser' ? user.id : `adv-${index}`,
            email: userRole === 'advertiser' ? user.email : `advertiser${index + 1}@example.com`
          }
        },
        publisher: {
          id: submission.publisherId || `pub-${index}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`
        },
        videoUrl: submission.videoUrl,
        tiktokUrl: submission.tiktokUrl,
        status: submission.status || (userRole === 'staff' ? 'pending' : 'advertiser_review'),
        feedback: submission.feedback,
        submittedAt: submission.submittedAt || new Date().toISOString(),
        updatedAt: submission.updatedAt || new Date().toISOString(),
        spReviewer: submission.spReviewerId ? {
          id: submission.spReviewerId,
          email: userRole === 'staff' ? user.email : '<EMAIL>'
        } : undefined,
        advertiserReviewer: submission.advertiserReviewerId ? {
          id: submission.advertiserReviewerId,
          email: userRole === 'advertiser' ? user.email : '<EMAIL>'
        } : undefined
      })) : [];
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setVideoSubmissions(submissionsWithMockData);
        setLastUpdated(new Date());
      }
    } catch (err) {
      if (mountedRef.current) {
        setError(err instanceof Error ? err.message : 'Failed to fetch video submissions');
      }
    }
  }, [user, userRole]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchVideoSubmissions();
    setLoading(false);
  }, [fetchVideoSubmissions]);

  // Start polling
  const startPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }
    
    pollingRef.current = setInterval(() => {
      if (mountedRef.current) {
        fetchVideoSubmissions();
      }
    }, pollingInterval);
  }, [fetchVideoSubmissions, pollingInterval]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Initial fetch and setup polling
  useEffect(() => {
    mountedRef.current = true;
    
    if (user && ((userRole === 'staff' && user.role === 'staff') || (userRole === 'advertiser' && user.role === 'advertiser'))) {
      const fetchData = async () => {
        setLoading(true);
        await fetchVideoSubmissions();
        setLoading(false);
      };
      
      fetchData();
      startPolling();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
    };
  }, [user, userRole, fetchVideoSubmissions, startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (user && ((userRole === 'staff' && user.role === 'staff') || (userRole === 'advertiser' && user.role === 'advertiser'))) {
        startPolling();
        // Fetch immediately when tab becomes visible
        fetchVideoSubmissions();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, userRole, fetchVideoSubmissions, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    videoSubmissions,
    loading,
    error,
    refetch,
    lastUpdated
  };
};

export default useRealTimeVideoReview;
