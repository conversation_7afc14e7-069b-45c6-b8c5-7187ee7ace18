'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  applicationStatus?: 'pending' | 'sp_approved' | 'approved' | 'rejected';
}

interface UseRealTimeVideoSubmissionsReturn {
  videoSubmissions: VideoSubmission[];
  availableCampaigns: Campaign[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const useRealTimeVideoSubmissions = (
  pollingInterval: number = 30000 // 30 seconds default
): UseRealTimeVideoSubmissionsReturn => {
  const user = useSelector(selectUser);
  const [videoSubmissions, setVideoSubmissions] = useState<VideoSubmission[]>([]);
  const [availableCampaigns, setAvailableCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const fetchVideoSubmissions = useCallback(async () => {
    if (!user?.id || user.role !== 'publisher') {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      const response = await fetch(`http://localhost:3000/video-submission`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Publisher role required.');
        }
        throw new Error(`Failed to fetch video submissions: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform data to include mock relations (in real app, backend should provide this)
      const submissionsWithMockData = Array.isArray(data) ? data.map((submission: any, index: number) => ({
        id: submission.id || `sub-${index}`,
        campaign: {
          id: submission.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: `adv-${index}`,
            email: `advertiser${index + 1}@example.com`
          }
        },
        publisher: {
          id: user.id,
          email: user.email,
          username: user.username || `publisher`
        },
        videoUrl: submission.videoUrl,
        tiktokUrl: submission.tiktokUrl,
        status: submission.status || 'pending',
        feedback: submission.feedback,
        submittedAt: submission.submittedAt || new Date().toISOString(),
        updatedAt: submission.updatedAt || new Date().toISOString(),
        spReviewer: submission.spReviewerId ? {
          id: submission.spReviewerId,
          email: `<EMAIL>`
        } : undefined,
        advertiserReviewer: submission.advertiserReviewerId ? {
          id: submission.advertiserReviewerId,
          email: `<EMAIL>`
        } : undefined
      })) : [];
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setVideoSubmissions(submissionsWithMockData);
        setLastUpdated(new Date());
      }
    } catch (err) {
      if (mountedRef.current) {
        setError(err instanceof Error ? err.message : 'Failed to fetch video submissions');
      }
    }
  }, [user]);

  const fetchAvailableCampaigns = useCallback(async () => {
    if (!user?.id || user.role !== 'publisher') {
      return;
    }

    try {
      // Mock data for now - in real app, fetch campaigns where user has approved applications
      const mockCampaigns: Campaign[] = [
        {
          id: 'camp-1',
          title: 'Summer Fashion Campaign',
          description: 'Showcase summer fashion trends',
          status: 'active',
          applicationStatus: 'approved'
        },
        {
          id: 'camp-2',
          title: 'Tech Product Launch',
          description: 'Promote new tech product',
          status: 'active',
          applicationStatus: 'approved'
        },
        {
          id: 'camp-3',
          title: 'Fitness Challenge',
          description: 'Promote healthy lifestyle',
          status: 'active',
          applicationStatus: 'approved'
        }
      ];
      
      if (mountedRef.current) {
        setAvailableCampaigns(mockCampaigns);
      }
    } catch (err) {
      console.error('Failed to fetch available campaigns:', err);
    }
  }, [user]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    await Promise.all([
      fetchVideoSubmissions(),
      fetchAvailableCampaigns()
    ]);
    setLoading(false);
  }, [fetchVideoSubmissions, fetchAvailableCampaigns]);

  // Start polling
  const startPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }
    
    pollingRef.current = setInterval(() => {
      if (mountedRef.current) {
        fetchVideoSubmissions();
        fetchAvailableCampaigns();
      }
    }, pollingInterval);
  }, [fetchVideoSubmissions, fetchAvailableCampaigns, pollingInterval]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Initial fetch and setup polling
  useEffect(() => {
    mountedRef.current = true;
    
    if (user?.role === 'publisher') {
      const fetchData = async () => {
        setLoading(true);
        await Promise.all([
          fetchVideoSubmissions(),
          fetchAvailableCampaigns()
        ]);
        setLoading(false);
      };
      
      fetchData();
      startPolling();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
    };
  }, [user, fetchVideoSubmissions, fetchAvailableCampaigns, startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (user?.role === 'publisher') {
        startPolling();
        // Fetch immediately when tab becomes visible
        fetchVideoSubmissions();
        fetchAvailableCampaigns();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, fetchVideoSubmissions, fetchAvailableCampaigns, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    videoSubmissions,
    availableCampaigns,
    loading,
    error,
    refetch,
    lastUpdated
  };
};

export default useRealTimeVideoSubmissions;
