'use client';
import { useState, useEffect, useCallback, useRef } from 'react';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  reviewer?: {
    id: string;
    email: string;
  };
}

interface UseRealTimeApplicationsOptions {
  pollingInterval?: number;
  enabled?: boolean;
}

interface UseRealTimeApplicationsReturn {
  applications: Application[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateApplication: (applicationId: string, updates: Partial<Application>) => void;
  updateApplications: (updates: Array<{ id: string; updates: Partial<Application> }>) => void;
}

export const useRealTimeApplications = (
  options: UseRealTimeApplicationsOptions = {}
): UseRealTimeApplicationsReturn => {
  const { pollingInterval = 30000, enabled = true } = options; // Poll every 30 seconds by default
  
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchRef = useRef<number>(0);

  // Fetch applications from API
  const fetchApplications = useCallback(async () => {
    try {
      setError(null);
      
      const response = await fetch(`http://localhost:3000/applications`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. SP Team role required.');
        }
        throw new Error(`Failed to fetch applications: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform data to include mock relations (in real app, backend should provide this)
      const applicationsWithMockData = Array.isArray(data) ? data.map((app: any, index: number) => ({
        id: app.id || `app-${index}`,
        campaign: {
          id: app.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: `adv-${index}`,
            email: `advertiser${index + 1}@example.com`
          }
        },
        publisher: {
          id: app.publisherId || `pub-${index}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`
        },
        status: app.status || 'pending',
        feedback: app.feedback,
        createdAt: app.createdAt || new Date().toISOString(),
        updatedAt: app.updatedAt || new Date().toISOString(),
        reviewer: app.reviewerId ? {
          id: app.reviewerId,
          email: `<EMAIL>`
        } : undefined
      })) : [];
      
      setApplications(applicationsWithMockData);
      lastFetchRef.current = Date.now();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch applications');
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      fetchApplications();
    }
  }, [fetchApplications, enabled]);

  // Set up polling
  useEffect(() => {
    if (!enabled || pollingInterval <= 0) return;

    intervalRef.current = setInterval(() => {
      // Only fetch if the last fetch was more than pollingInterval ago
      if (Date.now() - lastFetchRef.current >= pollingInterval) {
        fetchApplications();
      }
    }, pollingInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [fetchApplications, pollingInterval, enabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Optimistic update for single application
  const updateApplication = useCallback((applicationId: string, updates: Partial<Application>) => {
    setApplications(prev => prev.map(app => 
      app.id === applicationId 
        ? { ...app, ...updates, updatedAt: new Date().toISOString() }
        : app
    ));
  }, []);

  // Optimistic update for multiple applications
  const updateApplications = useCallback((updates: Array<{ id: string; updates: Partial<Application> }>) => {
    setApplications(prev => prev.map(app => {
      const update = updates.find(u => u.id === app.id);
      return update 
        ? { ...app, ...update.updates, updatedAt: new Date().toISOString() }
        : app;
    }));
  }, []);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchApplications();
  }, [fetchApplications]);

  return {
    applications,
    loading,
    error,
    refetch,
    updateApplication,
    updateApplications,
  };
};
