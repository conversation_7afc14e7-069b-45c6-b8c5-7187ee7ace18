'use client';
import React, { useState } from 'react';

interface VideoReviewActionsProps {
  submissionId: string;
  currentStatus: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  onApprove: (submissionId: string, feedback?: string) => Promise<void>;
  onReject: (submissionId: string, feedback: string) => Promise<void>;
  disabled?: boolean;
  userRole: 'staff' | 'advertiser';
  className?: string;
}

const VideoReviewActions: React.FC<VideoReviewActionsProps> = ({
  submissionId,
  currentStatus,
  onApprove,
  onReject,
  disabled = false,
  userRole,
  className = ''
}) => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackAction, setFeedbackAction] = useState<'approve' | 'reject'>('approve');
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user can review this submission
  const canReview = () => {
    if (userRole === 'staff') {
      return ['pending', 'sp_review'].includes(currentStatus);
    } else if (userRole === 'advertiser') {
      return currentStatus === 'advertiser_review';
    }
    return false;
  };

  // Handle approve action
  const handleApprove = () => {
    setFeedbackAction('approve');
    setFeedback('');
    setShowFeedbackModal(true);
  };

  // Handle reject action
  const handleReject = () => {
    setFeedbackAction('reject');
    setFeedback('');
    setShowFeedbackModal(true);
  };

  // Handle feedback submission
  const handleSubmitFeedback = async () => {
    if (feedbackAction === 'reject' && !feedback.trim()) {
      alert('Please provide feedback for rejection.');
      return;
    }

    setIsSubmitting(true);
    try {
      if (feedbackAction === 'approve') {
        await onApprove(submissionId, feedback.trim() || undefined);
      } else {
        await onReject(submissionId, feedback.trim());
      }
      
      setShowFeedbackModal(false);
      setFeedback('');
    } catch (error) {
      console.error('Review action failed:', error);
      alert(`Failed to ${feedbackAction} submission. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel feedback
  const handleCancelFeedback = () => {
    setShowFeedbackModal(false);
    setFeedback('');
  };

  // Get status badge
  const getStatusBadge = () => {
    const badges = {
      pending: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      sp_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      advertiser_review: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      published: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    };

    const labels = {
      pending: 'Pending',
      sp_review: 'SP Team Review',
      advertiser_review: 'Advertiser Review',
      approved: 'Approved',
      rejected: 'Rejected',
      published: 'Published'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[currentStatus]}`}>
        {labels[currentStatus]}
      </span>
    );
  };

  // Get next status after approval
  const getNextStatus = () => {
    if (userRole === 'staff') {
      return 'Advertiser Review';
    } else if (userRole === 'advertiser') {
      return 'Approved';
    }
    return 'Approved';
  };

  if (!canReview()) {
    return (
      <div className={`p-4 bg-gray-50 dark:bg-gray-800 rounded-lg ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">Current Status</h4>
            {getStatusBadge()}
          </div>
          <div className="text-gray-500 text-sm">
            {currentStatus === 'approved' && 'Video has been approved'}
            {currentStatus === 'rejected' && 'Video has been rejected'}
            {currentStatus === 'published' && 'Video has been published'}
            {currentStatus === 'pending' && userRole === 'advertiser' && 'Waiting for SP Team review'}
            {currentStatus === 'sp_review' && userRole === 'advertiser' && 'Under SP Team review'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">Review Actions</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Current status: {getStatusBadge()}
            </p>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={handleApprove}
            disabled={disabled || isSubmitting}
            className="btn btn-success flex-1"
          >
            <span className="mr-2">✅</span>
            Approve
          </button>
          <button
            onClick={handleReject}
            disabled={disabled || isSubmitting}
            className="btn btn-danger flex-1"
          >
            <span className="mr-2">❌</span>
            Reject
          </button>
        </div>

        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          {userRole === 'staff' && (
            <p>Approving will send to advertiser for final review</p>
          )}
          {userRole === 'advertiser' && (
            <p>Approving will mark the video as approved for publication</p>
          )}
        </div>
      </div>

      {/* Feedback Modal */}
      {showFeedbackModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {feedbackAction === 'approve' ? 'Approve Video' : 'Reject Video'}
              </h3>
              <button
                onClick={handleCancelFeedback}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                disabled={isSubmitting}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="mb-4">
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  {feedbackAction === 'approve' 
                    ? `This video will be moved to "${getNextStatus()}" status.`
                    : 'This video will be rejected and returned to the publisher.'
                  }
                </p>
              </div>

              <div className="mb-4">
                <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {feedbackAction === 'approve' ? 'Comments (Optional)' : 'Rejection Reason (Required)'}
                </label>
                <textarea
                  id="feedback"
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder={
                    feedbackAction === 'approve' 
                      ? 'Add any comments or notes...'
                      : 'Please explain why this video is being rejected...'
                  }
                  className="form-textarea w-full h-24"
                  disabled={isSubmitting}
                  required={feedbackAction === 'reject'}
                />
              </div>

              {feedbackAction === 'reject' && (
                <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                    <strong>Note:</strong> Providing clear feedback helps publishers understand what needs to be improved for resubmission.
                  </p>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleCancelFeedback}
                className="btn btn-outline-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitFeedback}
                className={`btn ${feedbackAction === 'approve' ? 'btn-success' : 'btn-danger'}`}
                disabled={isSubmitting || (feedbackAction === 'reject' && !feedback.trim())}
              >
                {isSubmitting ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  `${feedbackAction === 'approve' ? 'Approve' : 'Reject'} Video`
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default React.memo(VideoReviewActions);
