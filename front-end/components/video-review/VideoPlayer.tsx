'use client';
import React, { useState, useRef, useEffect } from 'react';

interface VideoPlayerProps {
  videoUrl?: string;
  tiktokUrl?: string;
  title: string;
  className?: string;
  autoPlay?: boolean;
  controls?: boolean;
  onLoadError?: (error: string) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  tiktokUrl,
  title,
  className = '',
  autoPlay = false,
  controls = true,
  onLoadError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle video load
  const handleVideoLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  // Handle video error
  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const errorMessage = 'Failed to load video. Please check the video URL.';
    setError(errorMessage);
    setIsLoading(false);
    onLoadError?.(errorMessage);
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // Handle video play/pause events
  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);

  // Extract TikTok video ID for embed
  const getTikTokEmbedId = (url: string): string | null => {
    const patterns = [
      /tiktok\.com\/@[\w.-]+\/video\/(\d+)/,
      /vm\.tiktok\.com\/([\w]+)/,
      /tiktok\.com\/t\/([\w]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  // Reset states when URLs change
  useEffect(() => {
    setIsLoading(true);
    setError(null);
    setIsPlaying(false);
  }, [videoUrl, tiktokUrl]);

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-8 ${className}`}>
        <div className="text-red-500 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Video Load Error</h3>
        <p className="text-gray-600 dark:text-gray-400 text-center">{error}</p>
        <button
          onClick={() => {
            setError(null);
            setIsLoading(true);
          }}
          className="mt-4 btn btn-sm btn-primary"
        >
          Retry
        </button>
      </div>
    );
  }

  // TikTok video display
  if (tiktokUrl) {
    const embedId = getTikTokEmbedId(tiktokUrl);
    
    return (
      <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        )}
        
        <div className="aspect-[9/16] max-w-sm mx-auto">
          {embedId ? (
            <iframe
              src={`https://www.tiktok.com/embed/v2/${embedId}`}
              className="w-full h-full"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              title={title}
              onLoad={() => setIsLoading(false)}
              onError={() => {
                const errorMessage = 'Failed to load TikTok video embed.';
                setError(errorMessage);
                setIsLoading(false);
                onLoadError?.(errorMessage);
              }}
            />
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center text-white p-6">
              <div className="text-6xl mb-4">📱</div>
              <h3 className="text-lg font-medium mb-2">TikTok Video</h3>
              <p className="text-gray-300 text-center mb-4">
                Unable to embed this TikTok video. View it directly on TikTok.
              </p>
              <a
                href={tiktokUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary"
                onClick={() => setIsLoading(false)}
              >
                View on TikTok
              </a>
            </div>
          )}
        </div>
        
        {/* TikTok URL Display */}
        <div className="p-4 bg-gray-900 text-white">
          <p className="text-sm text-gray-300 mb-1">TikTok URL:</p>
          <a
            href={tiktokUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-400 hover:text-blue-300 text-sm break-all"
          >
            {tiktokUrl}
          </a>
        </div>
      </div>
    );
  }

  // Regular video display
  if (videoUrl) {
    return (
      <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        )}
        
        <video
          ref={videoRef}
          className="w-full h-auto"
          controls={controls}
          autoPlay={autoPlay}
          preload="metadata"
          onLoadedData={handleVideoLoad}
          onError={handleVideoError}
          onPlay={handlePlay}
          onPause={handlePause}
          title={title}
        >
          <source src={videoUrl} type="video/mp4" />
          <source src={videoUrl} type="video/webm" />
          <source src={videoUrl} type="video/ogg" />
          Your browser does not support the video tag.
        </video>
        
        {/* Custom Controls Overlay (if needed) */}
        {!controls && (
          <div className="absolute inset-0 flex items-center justify-center">
            <button
              onClick={handlePlayPause}
              className="bg-black bg-opacity-50 text-white rounded-full p-4 hover:bg-opacity-70 transition-opacity"
              aria-label={isPlaying ? 'Pause video' : 'Play video'}
            >
              {isPlaying ? (
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
        )}
        
        {/* Video Info */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          <h3 className="text-white font-medium">{title}</h3>
        </div>
      </div>
    );
  }

  // No video provided
  return (
    <div className={`flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-8 ${className}`}>
      <div className="text-gray-400 text-6xl mb-4">🎬</div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Video Available</h3>
      <p className="text-gray-600 dark:text-gray-400 text-center">
        No video URL or TikTok link provided for this submission.
      </p>
    </div>
  );
};

export default React.memo(VideoPlayer);
