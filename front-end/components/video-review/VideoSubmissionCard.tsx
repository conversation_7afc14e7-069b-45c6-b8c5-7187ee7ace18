'use client';
import React, { useState } from 'react';
import VideoPlayer from './VideoPlayer';
import VideoReviewActions from './VideoReviewActions';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface VideoSubmissionCardProps {
  submission: VideoSubmission;
  onApprove: (submissionId: string, feedback?: string) => Promise<void>;
  onReject: (submissionId: string, feedback: string) => Promise<void>;
  userRole: 'staff' | 'advertiser';
  className?: string;
}

const VideoSubmissionCard: React.FC<VideoSubmissionCardProps> = ({
  submission,
  onApprove,
  onReject,
  userRole,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get submission type
  const getSubmissionType = () => {
    if (submission.videoUrl) return 'Video Upload';
    if (submission.tiktokUrl) return 'TikTok Link';
    return 'Unknown';
  };

  // Handle video load error
  const handleVideoError = (error: string) => {
    setVideoError(error);
  };

  return (
    <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm ${className}`}>
      {/* Card Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {submission.campaign.title}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Publisher:</span>
                <p className="font-medium text-gray-900 dark:text-white">
                  {submission.publisher.username || submission.publisher.email}
                </p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Submission Type:</span>
                <p className="font-medium text-gray-900 dark:text-white flex items-center">
                  <span className="mr-2">
                    {submission.videoUrl ? '🎬' : '📱'}
                  </span>
                  {getSubmissionType()}
                </p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Submitted:</span>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatDate(submission.submittedAt)}
                </p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Last Updated:</span>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatDate(submission.updatedAt)}
                </p>
              </div>
            </div>
          </div>
          
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-4 btn btn-sm btn-outline-secondary"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </button>
        </div>

        {/* Campaign Info */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-sm">
            <span className="text-gray-600 dark:text-gray-400">Campaign Advertiser:</span>
            <p className="font-medium text-gray-900 dark:text-white">
              {submission.campaign.advertiser.email}
            </p>
          </div>
        </div>

        {/* Existing Feedback */}
        {submission.feedback && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Previous Feedback</h5>
            <p className="text-yellow-700 dark:text-yellow-300 text-sm">{submission.feedback}</p>
          </div>
        )}
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="p-6 space-y-6">
          {/* Video Player */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Video Content</h4>
            <div className="max-w-2xl">
              <VideoPlayer
                videoUrl={submission.videoUrl}
                tiktokUrl={submission.tiktokUrl}
                title={`${submission.campaign.title} - ${submission.publisher.username || submission.publisher.email}`}
                onLoadError={handleVideoError}
                className="w-full"
              />
            </div>
            
            {videoError && (
              <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <p className="text-red-700 dark:text-red-300 text-sm">
                  <strong>Video Error:</strong> {videoError}
                </p>
              </div>
            )}
          </div>

          {/* Review Actions */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Review Actions</h4>
            <VideoReviewActions
              submissionId={submission.id}
              currentStatus={submission.status}
              onApprove={onApprove}
              onReject={onReject}
              userRole={userRole}
            />
          </div>

          {/* Submission Details */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Submission Details</h4>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Submission ID:</span>
                  <p className="font-mono text-gray-900 dark:text-white">{submission.id}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Campaign ID:</span>
                  <p className="font-mono text-gray-900 dark:text-white">{submission.campaign.id}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Publisher ID:</span>
                  <p className="font-mono text-gray-900 dark:text-white">{submission.publisher.id}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Publisher Email:</span>
                  <p className="font-medium text-gray-900 dark:text-white">{submission.publisher.email}</p>
                </div>
                
                {submission.videoUrl && (
                  <div className="md:col-span-2">
                    <span className="text-gray-600 dark:text-gray-400">Video URL:</span>
                    <p className="font-mono text-gray-900 dark:text-white break-all text-xs">
                      {submission.videoUrl}
                    </p>
                  </div>
                )}
                
                {submission.tiktokUrl && (
                  <div className="md:col-span-2">
                    <span className="text-gray-600 dark:text-gray-400">TikTok URL:</span>
                    <a
                      href={submission.tiktokUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:underline break-all text-xs"
                    >
                      {submission.tiktokUrl}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Review History */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Review History</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600 dark:text-gray-400">
                  Submitted by {submission.publisher.username || submission.publisher.email}
                </span>
                <span className="text-gray-500">{formatDate(submission.submittedAt)}</span>
              </div>
              
              {submission.spReviewer && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Reviewed by SP Team ({submission.spReviewer.email})
                  </span>
                  <span className="text-gray-500">{formatDate(submission.updatedAt)}</span>
                </div>
              )}
              
              {submission.advertiserReviewer && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">
                    Reviewed by Advertiser ({submission.advertiserReviewer.email})
                  </span>
                  <span className="text-gray-500">{formatDate(submission.updatedAt)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(VideoSubmissionCard);
