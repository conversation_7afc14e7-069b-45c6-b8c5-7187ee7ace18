'use client';
import React, { useState } from 'react';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
}

interface BulkReviewActionsProps {
  selectedSubmissions: string[];
  submissions: VideoSubmission[];
  onBulkApprove: (submissionIds: string[], feedback?: string) => Promise<void>;
  onBulkReject: (submissionIds: string[], feedback: string) => Promise<void>;
  onSelectAll: () => void;
  onClearSelection: () => void;
  userRole: 'staff' | 'advertiser';
  disabled?: boolean;
}

const BulkReviewActions: React.FC<BulkReviewActionsProps> = ({
  selectedSubmissions,
  submissions,
  onBulkApprove,
  onBulkReject,
  onSelectAll,
  onClearSelection,
  userRole,
  disabled = false
}) => {
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [bulkAction, setBulkAction] = useState<'approve' | 'reject'>('approve');
  const [bulkFeedback, setBulkFeedback] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get reviewable submissions based on user role
  const getReviewableSubmissions = () => {
    return submissions.filter(submission => {
      if (userRole === 'staff') {
        return ['pending', 'sp_review'].includes(submission.status);
      } else if (userRole === 'advertiser') {
        return submission.status === 'advertiser_review';
      }
      return false;
    });
  };

  const reviewableSubmissions = getReviewableSubmissions();
  const selectedReviewableSubmissions = selectedSubmissions.filter(id => 
    reviewableSubmissions.some(sub => sub.id === id)
  );

  // Handle bulk approve
  const handleBulkApprove = () => {
    if (selectedReviewableSubmissions.length === 0) return;
    setBulkAction('approve');
    setBulkFeedback('');
    setShowBulkModal(true);
  };

  // Handle bulk reject
  const handleBulkReject = () => {
    if (selectedReviewableSubmissions.length === 0) return;
    setBulkAction('reject');
    setBulkFeedback('');
    setShowBulkModal(true);
  };

  // Handle bulk action submission
  const handleBulkSubmit = async () => {
    if (bulkAction === 'reject' && !bulkFeedback.trim()) {
      alert('Please provide feedback for bulk rejection.');
      return;
    }

    setIsProcessing(true);
    try {
      if (bulkAction === 'approve') {
        await onBulkApprove(selectedReviewableSubmissions, bulkFeedback.trim() || undefined);
      } else {
        await onBulkReject(selectedReviewableSubmissions, bulkFeedback.trim());
      }
      
      setShowBulkModal(false);
      setBulkFeedback('');
      onClearSelection();
    } catch (error) {
      console.error('Bulk review action failed:', error);
      alert(`Failed to ${bulkAction} submissions. Please try again.`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle cancel bulk action
  const handleCancelBulk = () => {
    setShowBulkModal(false);
    setBulkFeedback('');
  };

  // Get next status after approval
  const getNextStatus = () => {
    if (userRole === 'staff') {
      return 'Advertiser Review';
    } else if (userRole === 'advertiser') {
      return 'Approved';
    }
    return 'Approved';
  };

  if (selectedSubmissions.length === 0) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Select submissions to perform bulk actions
          </div>
          <button
            onClick={onSelectAll}
            className="btn btn-sm btn-outline-secondary"
            disabled={reviewableSubmissions.length === 0}
          >
            Select All Reviewable ({reviewableSubmissions.length})
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
              {selectedSubmissions.length} submission{selectedSubmissions.length !== 1 ? 's' : ''} selected
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300">
              ({selectedReviewableSubmissions.length} reviewable)
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {selectedReviewableSubmissions.length > 0 && (
              <>
                <button
                  onClick={handleBulkApprove}
                  disabled={disabled || isProcessing}
                  className="btn btn-sm btn-success"
                >
                  <span className="mr-1">✅</span>
                  Bulk Approve ({selectedReviewableSubmissions.length})
                </button>
                <button
                  onClick={handleBulkReject}
                  disabled={disabled || isProcessing}
                  className="btn btn-sm btn-danger"
                >
                  <span className="mr-1">❌</span>
                  Bulk Reject ({selectedReviewableSubmissions.length})
                </button>
              </>
            )}
            <button
              onClick={onClearSelection}
              className="btn btn-sm btn-outline-secondary"
            >
              Clear Selection
            </button>
          </div>
        </div>

        {selectedReviewableSubmissions.length > 0 && (
          <div className="mt-3 text-xs text-blue-600 dark:text-blue-400">
            {userRole === 'staff' && (
              <p>Bulk approving will send all selected submissions to advertiser review</p>
            )}
            {userRole === 'advertiser' && (
              <p>Bulk approving will mark all selected submissions as approved for publication</p>
            )}
          </div>
        )}
      </div>

      {/* Bulk Action Modal */}
      {showBulkModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Bulk {bulkAction === 'approve' ? 'Approve' : 'Reject'} Submissions
              </h3>
              <button
                onClick={handleCancelBulk}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                disabled={isProcessing}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="mb-4">
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  You are about to {bulkAction} {selectedReviewableSubmissions.length} submission{selectedReviewableSubmissions.length !== 1 ? 's' : ''}.
                </p>
                {bulkAction === 'approve' && (
                  <p className="text-gray-600 dark:text-gray-400 mb-2">
                    These submissions will be moved to "{getNextStatus()}" status.
                  </p>
                )}
                {bulkAction === 'reject' && (
                  <p className="text-gray-600 dark:text-gray-400 mb-2">
                    These submissions will be rejected and returned to publishers.
                  </p>
                )}
              </div>

              {/* Selected Submissions List */}
              <div className="mb-4 max-h-40 overflow-y-auto">
                <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Selected Submissions:
                </h5>
                <div className="space-y-1">
                  {selectedReviewableSubmissions.map(id => {
                    const submission = submissions.find(sub => sub.id === id);
                    return submission ? (
                      <div key={id} className="text-xs text-gray-600 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                        {submission.campaign.title} - {submission.publisher.username || submission.publisher.email}
                      </div>
                    ) : null;
                  })}
                </div>
              </div>

              <div className="mb-4">
                <label htmlFor="bulkFeedback" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {bulkAction === 'approve' ? 'Comments (Optional)' : 'Rejection Reason (Required)'}
                </label>
                <textarea
                  id="bulkFeedback"
                  value={bulkFeedback}
                  onChange={(e) => setBulkFeedback(e.target.value)}
                  placeholder={
                    bulkAction === 'approve' 
                      ? 'Add any comments or notes for all submissions...'
                      : 'Please explain why these submissions are being rejected...'
                  }
                  className="form-textarea w-full h-24"
                  disabled={isProcessing}
                  required={bulkAction === 'reject'}
                />
              </div>

              {bulkAction === 'reject' && (
                <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                    <strong>Note:</strong> The same feedback will be applied to all selected submissions. 
                    Consider providing general guidance that applies to all.
                  </p>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleCancelBulk}
                className="btn btn-outline-secondary"
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button
                onClick={handleBulkSubmit}
                className={`btn ${bulkAction === 'approve' ? 'btn-success' : 'btn-danger'}`}
                disabled={isProcessing || (bulkAction === 'reject' && !bulkFeedback.trim())}
              >
                {isProcessing ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing...</span>
                  </div>
                ) : (
                  `${bulkAction === 'approve' ? 'Approve' : 'Reject'} ${selectedReviewableSubmissions.length} Submission${selectedReviewableSubmissions.length !== 1 ? 's' : ''}`
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default React.memo(BulkReviewActions);
