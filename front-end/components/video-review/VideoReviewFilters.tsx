'use client';
import React from 'react';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
  submissionType: string;
}

interface VideoReviewFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  submissions: VideoSubmission[];
  filteredCount?: number;
  userRole: 'staff' | 'advertiser';
}

const VideoReviewFilters: React.FC<VideoReviewFiltersProps> = ({
  filters,
  onFilterChange,
  submissions,
  filteredCount,
  userRole
}) => {
  // Calculate status counts based on user role
  const getStatusCounts = () => {
    if (userRole === 'staff') {
      return {
        total: submissions.length,
        pending: submissions.filter(sub => sub.status === 'pending').length,
        sp_review: submissions.filter(sub => sub.status === 'sp_review').length,
        advertiser_review: submissions.filter(sub => sub.status === 'advertiser_review').length,
        approved: submissions.filter(sub => sub.status === 'approved').length,
        rejected: submissions.filter(sub => sub.status === 'rejected').length,
        published: submissions.filter(sub => sub.status === 'published').length,
      };
    } else {
      return {
        total: submissions.length,
        advertiser_review: submissions.filter(sub => sub.status === 'advertiser_review').length,
        approved: submissions.filter(sub => sub.status === 'approved').length,
        rejected: submissions.filter(sub => sub.status === 'rejected').length,
        published: submissions.filter(sub => sub.status === 'published').length,
      };
    }
  };

  const statusCounts = getStatusCounts();

  // Get unique campaigns for filter dropdown
  const uniqueCampaigns = Array.from(
    new Map(submissions.map(sub => [sub.campaign.id, sub.campaign])).values()
  );

  // Clear all filters
  const handleClearFilters = () => {
    onFilterChange({
      status: 'all',
      search: '',
      campaignId: 'all',
      submissionType: 'all'
    });
  };

  // Get available status options based on user role
  const getStatusOptions = () => {
    if (userRole === 'staff') {
      return [
        { value: 'all', label: 'All Statuses' },
        { value: 'pending', label: 'Pending' },
        { value: 'sp_review', label: 'SP Team Review' },
        { value: 'advertiser_review', label: 'Advertiser Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'published', label: 'Published' }
      ];
    } else {
      return [
        { value: 'all', label: 'All Statuses' },
        { value: 'advertiser_review', label: 'Pending Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'rejected', label: 'Rejected' },
        { value: 'published', label: 'Published' }
      ];
    }
  };

  return (
    <div className="panel p-6 space-y-6">
      {/* Status Summary Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total</p>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{statusCounts.total}</p>
            </div>
            <div className="text-blue-500 text-2xl">📊</div>
          </div>
        </div>

        {userRole === 'staff' && (
          <>
            <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg border border-gray-200 dark:border-gray-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{statusCounts.pending}</p>
                </div>
                <div className="text-gray-500 text-2xl">⏳</div>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">SP Review</p>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{statusCounts.sp_review}</p>
                </div>
                <div className="text-blue-500 text-2xl">👥</div>
              </div>
            </div>
          </>
        )}

        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                {userRole === 'staff' ? 'Advertiser Review' : 'Pending Review'}
              </p>
              <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                {statusCounts.advertiser_review || 0}
              </p>
            </div>
            <div className="text-yellow-500 text-2xl">🔍</div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Approved</p>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{statusCounts.approved || 0}</p>
            </div>
            <div className="text-green-500 text-2xl">✅</div>
          </div>
        </div>

        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600 dark:text-red-400">Rejected</p>
              <p className="text-2xl font-bold text-red-900 dark:text-red-100">{statusCounts.rejected || 0}</p>
            </div>
            <div className="text-red-500 text-2xl">❌</div>
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Submissions
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="search"
              className="form-input pl-10"
              placeholder="Search by campaign or publisher..."
              value={filters.search}
              onChange={(e) => onFilterChange({ search: e.target.value })}
              aria-label="Search submissions by campaign title or publisher"
            />
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Status
          </label>
          <select
            id="status"
            className="form-select"
            value={filters.status}
            onChange={(e) => onFilterChange({ status: e.target.value })}
            aria-label="Filter submissions by status"
          >
            {getStatusOptions().map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Campaign Filter */}
        <div>
          <label htmlFor="campaign" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Campaign
          </label>
          <select
            id="campaign"
            className="form-select"
            value={filters.campaignId}
            onChange={(e) => onFilterChange({ campaignId: e.target.value })}
            aria-label="Filter submissions by campaign"
          >
            <option value="all">All Campaigns</option>
            {uniqueCampaigns.map(campaign => (
              <option key={campaign.id} value={campaign.id}>
                {campaign.title}
              </option>
            ))}
          </select>
        </div>

        {/* Submission Type Filter */}
        <div>
          <label htmlFor="submissionType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Type
          </label>
          <select
            id="submissionType"
            className="form-select"
            value={filters.submissionType}
            onChange={(e) => onFilterChange({ submissionType: e.target.value })}
            aria-label="Filter submissions by type"
          >
            <option value="all">All Types</option>
            <option value="video">Video Upload</option>
            <option value="tiktok">TikTok Link</option>
          </select>
        </div>
      </div>

      {/* Filter Actions */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {filteredCount !== undefined && (
            <>Showing {filteredCount} of {submissions.length} submissions</>
          )}
        </div>
        <button
          onClick={handleClearFilters}
          className="btn btn-outline-secondary btn-sm"
          disabled={filters.status === 'all' && !filters.search && filters.campaignId === 'all' && filters.submissionType === 'all'}
        >
          Clear Filters
        </button>
      </div>

      {/* Active Filters Display */}
      {(filters.status !== 'all' || filters.search || filters.campaignId !== 'all' || filters.submissionType !== 'all') && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Active filters:</span>
          
          {filters.status !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Status: {getStatusOptions().find(opt => opt.value === filters.status)?.label || filters.status}
              <button
                onClick={() => onFilterChange({ status: 'all' })}
                className="ml-1 text-blue-600 hover:text-blue-800"
                aria-label="Remove status filter"
              >
                ×
              </button>
            </span>
          )}
          
          {filters.search && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Search: "{filters.search}"
              <button
                onClick={() => onFilterChange({ search: '' })}
                className="ml-1 text-green-600 hover:text-green-800"
                aria-label="Remove search filter"
              >
                ×
              </button>
            </span>
          )}
          
          {filters.campaignId !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              Campaign: {uniqueCampaigns.find(c => c.id === filters.campaignId)?.title || 'Unknown'}
              <button
                onClick={() => onFilterChange({ campaignId: 'all' })}
                className="ml-1 text-purple-600 hover:text-purple-800"
                aria-label="Remove campaign filter"
              >
                ×
              </button>
            </span>
          )}

          {filters.submissionType !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
              Type: {filters.submissionType === 'video' ? 'Video Upload' : 'TikTok Link'}
              <button
                onClick={() => onFilterChange({ submissionType: 'all' })}
                className="ml-1 text-orange-600 hover:text-orange-800"
                aria-label="Remove type filter"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default React.memo(VideoReviewFilters);
