'use client';
import React, { useState } from 'react';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface AdvertiserBulkReviewModalProps {
  applications: Application[];
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => Promise<void>;
}

const AdvertiserBulkReviewModal: React.FC<AdvertiserBulkReviewModalProps> = ({
  applications,
  isOpen,
  onClose,
  onSubmit
}) => {
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'reject' | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  if (!isOpen || applications.length === 0) return null;

  const handleActionClick = (action: 'approve' | 'reject') => {
    if (action === 'reject' && !feedback.trim()) {
      alert('Please provide feedback for bulk rejection');
      return;
    }
    setSelectedAction(action);
    setShowConfirmation(true);
  };

  const handleConfirmSubmit = async () => {
    if (!selectedAction) return;

    setIsSubmitting(true);
    try {
      const applicationIds = applications.map(app => app.id);
      await onSubmit(applicationIds, selectedAction, feedback.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to submit bulk review:', error);
      alert('Failed to submit bulk review. Please try again.');
    } finally {
      setIsSubmitting(false);
      setShowConfirmation(false);
      setSelectedAction(null);
    }
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    setSelectedAction(null);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback('');
      setSelectedAction(null);
      setShowConfirmation(false);
      onClose();
    }
  };

  // Group applications by campaign for better overview
  const applicationsByCampaign = applications.reduce((acc, app) => {
    const campaignId = app.campaign.id;
    if (!acc[campaignId]) {
      acc[campaignId] = {
        campaign: app.campaign,
        applications: []
      };
    }
    acc[campaignId].applications.push(app);
    return acc;
  }, {} as Record<string, { campaign: Application['campaign'], applications: Application[] }>);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Bulk Application Review
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={isSubmitting}
            aria-label="Close modal"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Bulk Review Overview */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start justify-between">
              <div>
                <h4 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Bulk Review - {applications.length} Applications
                </h4>
                <p className="text-blue-700 dark:text-blue-300 text-sm">
                  You are about to review {applications.length} applications at once. This action will apply the same decision and feedback to all selected applications.
                </p>
              </div>
              <div className="text-blue-500 text-3xl">📋</div>
            </div>
          </div>

          {/* Applications Summary */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Applications to Review ({applications.length})
            </h4>
            
            <div className="max-h-64 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
              {Object.entries(applicationsByCampaign).map(([campaignId, { campaign, applications: campaignApps }]) => (
                <div key={campaignId} className="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                  <div className="bg-gray-50 dark:bg-gray-700 px-4 py-2">
                    <h5 className="font-medium text-gray-900 dark:text-white">
                      {campaign.title} ({campaignApps.length} applications)
                    </h5>
                  </div>
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {campaignApps.map((app) => (
                      <div key={app.id} className="px-4 py-3 flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {app.publisher.username || app.publisher.email}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Applied: {new Date(app.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          Awaiting Review
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bulk Feedback */}
          <div>
            <label htmlFor="bulk-feedback" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bulk Decision Feedback {selectedAction === 'reject' && <span className="text-red-500">*</span>}
            </label>
            <textarea
              id="bulk-feedback"
              rows={4}
              className="form-textarea w-full"
              placeholder={selectedAction === 'approve' ? 
                "Optional: Add comments about your bulk approval decision..." : 
                "Required: Please provide detailed feedback explaining your bulk rejection decision..."
              }
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              disabled={isSubmitting}
            />
            <p className="text-xs text-gray-500 mt-1">
              This feedback will be applied to all {applications.length} selected applications.
            </p>
            {selectedAction === 'reject' && (
              <p className="text-xs text-red-600 mt-1">
                Feedback is required when rejecting applications
              </p>
            )}
          </div>

          {/* Warning */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-start">
              <div className="text-yellow-500 text-xl mr-3">⚠️</div>
              <div>
                <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Important Notice
                </h5>
                <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                  Bulk review actions cannot be undone. Please ensure you want to apply the same decision to all {applications.length} applications before proceeding.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          {!showConfirmation ? (
            <>
              <button
                onClick={handleClose}
                className="btn btn-outline-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={() => handleActionClick('reject')}
                className="btn btn-danger"
                disabled={isSubmitting}
              >
                Reject All ({applications.length})
              </button>
              <button
                onClick={() => handleActionClick('approve')}
                className="btn btn-success"
                disabled={isSubmitting}
              >
                Approve All ({applications.length})
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleCancelConfirmation}
                className="btn btn-outline-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSubmit}
                className={`btn ${selectedAction === 'approve' ? 'btn-success' : 'btn-danger'}`}
                disabled={isSubmitting}
              >
                {isSubmitting 
                  ? `${selectedAction === 'approve' ? 'Approving' : 'Rejecting'} ${applications.length} Applications...` 
                  : `Confirm ${selectedAction === 'approve' ? 'Approval' : 'Rejection'} of ${applications.length} Applications`
                }
              </button>
            </>
          )}
        </div>

        {/* Confirmation Message */}
        {showConfirmation && (
          <div className="px-6 pb-4">
            <div className={`p-3 rounded-lg ${
              selectedAction === 'approve' 
                ? 'bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800' 
                : 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'
            }`}>
              <p className={`text-sm ${
                selectedAction === 'approve' 
                  ? 'text-green-800 dark:text-green-200' 
                  : 'text-red-800 dark:text-red-200'
              }`}>
                Are you sure you want to {selectedAction} all {applications.length} applications? This action cannot be undone and will apply your decision to all selected applications.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvertiserBulkReviewModal;
