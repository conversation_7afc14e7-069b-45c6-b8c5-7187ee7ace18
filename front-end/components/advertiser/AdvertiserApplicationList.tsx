'use client';
import React, { useState } from 'react';
import { DataTable } from 'mantine-datatable';
import AdvertiserApplicationReviewModal from './AdvertiserApplicationReviewModal';
import AdvertiserBulkReviewModal from './AdvertiserBulkReviewModal';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface AdvertiserApplicationListProps {
  applications: Application[];
  onReviewApplication: (applicationId: string, action: 'approve' | 'reject', feedback?: string) => Promise<void>;
  onBulkReviewApplications: (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => Promise<void>;
  loading: boolean;
}

const AdvertiserApplicationList: React.FC<AdvertiserApplicationListProps> = ({
  applications,
  onReviewApplication,
  onBulkReviewApplications,
  loading
}) => {
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [selectedApplicationIds, setSelectedApplicationIds] = useState<string[]>([]);
  const [bulkReviewModalOpen, setBulkReviewModalOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Handle review action
  const handleReviewClick = (application: Application) => {
    setSelectedApplication(application);
    setReviewModalOpen(true);
  };

  // Handle review submission
  const handleReviewSubmit = async (action: 'approve' | 'reject', feedback?: string) => {
    if (selectedApplication) {
      await onReviewApplication(selectedApplication.id, action, feedback);
      setReviewModalOpen(false);
      setSelectedApplication(null);
    }
  };

  // Handle bulk selection
  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    if (checked) {
      setSelectedApplicationIds(prev => [...prev, applicationId]);
    } else {
      setSelectedApplicationIds(prev => prev.filter(id => id !== applicationId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const reviewableApps = paginatedApplications.filter(app => app.status === 'sp_approved');
      setSelectedApplicationIds(reviewableApps.map(app => app.id));
    } else {
      setSelectedApplicationIds([]);
    }
  };

  // Handle bulk review
  const handleBulkReview = () => {
    setBulkReviewModalOpen(true);
  };

  const handleBulkReviewSubmit = async (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => {
    await onBulkReviewApplications(applicationIds, action, feedback);
    setSelectedApplicationIds([]);
    setBulkReviewModalOpen(false);
  };

  // Filter applications for pagination
  const paginatedApplications = applications.slice((page - 1) * pageSize, page * pageSize);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const badges = {
      sp_approved: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      pending: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    };

    const labels = {
      sp_approved: 'Awaiting Review',
      approved: 'Approved',
      rejected: 'Rejected',
      pending: 'Pending'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status as keyof typeof badges] || badges.pending}`}>
        {labels[status as keyof typeof labels] || status}
      </span>
    );
  };

  // Get selected applications for bulk review
  const selectedApplications = applications.filter(app => selectedApplicationIds.includes(app.id));
  const reviewableApplications = paginatedApplications.filter(app => app.status === 'sp_approved');
  const allReviewableSelected = reviewableApplications.length > 0 && reviewableApplications.every(app => selectedApplicationIds.includes(app.id));

  // Table columns configuration
  const columns = [
    {
      accessor: 'select',
      title: (
        <input
          type="checkbox"
          checked={allReviewableSelected}
          onChange={(e) => handleSelectAll(e.target.checked)}
          disabled={reviewableApplications.length === 0}
          className="form-checkbox"
          aria-label="Select all reviewable applications"
        />
      ),
      render: (application: Application) => (
        <input
          type="checkbox"
          checked={selectedApplicationIds.includes(application.id)}
          onChange={(e) => handleSelectApplication(application.id, e.target.checked)}
          disabled={application.status !== 'sp_approved'}
          className="form-checkbox"
          aria-label={`Select application from ${application.publisher.username || application.publisher.email}`}
        />
      ),
    },
    {
      accessor: 'publisher.email',
      title: 'Publisher',
      sortable: true,
      render: (application: Application) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {application.publisher.username || application.publisher.email}
          </span>
          {application.publisher.username && (
            <span className="text-sm text-gray-500">{application.publisher.email}</span>
          )}
        </div>
      ),
    },
    {
      accessor: 'campaign.title',
      title: 'Campaign',
      sortable: true,
      render: (application: Application) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {application.campaign.title}
          </span>
          <span className="text-sm text-gray-500">ID: {application.campaign.id}</span>
        </div>
      ),
    },
    {
      accessor: 'status',
      title: 'Status',
      sortable: true,
      render: (application: Application) => getStatusBadge(application.status),
    },
    {
      accessor: 'createdAt',
      title: 'Applied',
      sortable: true,
      render: (application: Application) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(application.createdAt)}
        </div>
      ),
    },
    {
      accessor: 'spReviewer',
      title: 'SP Team Review',
      render: (application: Application) => (
        <div className="text-sm">
          {application.spReviewer ? (
            <div className="text-green-600 dark:text-green-400">
              ✓ Approved by SP Team
            </div>
          ) : (
            <div className="text-gray-500">
              Not reviewed
            </div>
          )}
        </div>
      ),
    },
    {
      accessor: 'actions',
      title: 'Actions',
      textAlignment: 'center' as const,
      render: (application: Application) => (
        <div className="flex items-center justify-center space-x-2">
          {application.status === 'sp_approved' ? (
            <button
              onClick={() => handleReviewClick(application)}
              className="btn btn-sm btn-primary"
              aria-label={`Review application from ${application.publisher.username || application.publisher.email}`}
            >
              Review
            </button>
          ) : (
            <button
              onClick={() => handleReviewClick(application)}
              className="btn btn-sm btn-outline-secondary"
              aria-label={`View application details from ${application.publisher.username || application.publisher.email}`}
            >
              View Details
            </button>
          )}
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="panel p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading applications...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="panel">
      {/* Table Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Applications ({applications.length})
          </h2>
          {selectedApplicationIds.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-blue-600 dark:text-blue-400">
                {selectedApplicationIds.length} selected
              </span>
              <button
                onClick={handleBulkReview}
                className="btn btn-sm btn-primary"
                disabled={selectedApplicationIds.length === 0}
              >
                Bulk Review ({selectedApplicationIds.length})
              </button>
              <button
                onClick={() => setSelectedApplicationIds([])}
                className="btn btn-sm btn-outline-secondary"
              >
                Clear Selection
              </button>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <label htmlFor="page-size" className="text-sm text-gray-600 dark:text-gray-400">Show:</label>
          <select
            id="page-size"
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value));
              setPage(1);
            }}
            className="form-select form-select-sm w-20"
            aria-label="Number of applications per page"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
      </div>

      {/* Data Table */}
      <div className="datatables">
        <DataTable
          className="whitespace-nowrap table-hover"
          records={paginatedApplications}
          columns={columns}
          totalRecords={applications.length}
          recordsPerPage={pageSize}
          page={page}
          onPageChange={(p) => setPage(p)}
          recordsPerPageOptions={[10, 25, 50, 100]}
          onRecordsPerPageChange={setPageSize}
          sortStatus={{ columnAccessor: 'createdAt', direction: 'desc' }}
          minHeight={200}
          noRecordsText="No applications found"
          noRecordsIcon={
            <div className="flex flex-col items-center justify-center py-12">
              <div className="text-6xl text-gray-300 dark:text-gray-600 mb-4">📋</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Applications Found</h3>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md">
                {applications.length === 0
                  ? "You don't have any applications yet. Applications will appear here once publishers apply to your campaigns."
                  : "No applications match your current filters. Try adjusting your search criteria."
                }
              </p>
            </div>
          }
        />
      </div>

      {/* Advanced Review Modal */}
      {selectedApplication && (
        <AdvertiserApplicationReviewModal
          application={selectedApplication}
          isOpen={reviewModalOpen}
          onClose={() => {
            setReviewModalOpen(false);
            setSelectedApplication(null);
          }}
          onSubmit={handleReviewSubmit}
        />
      )}

      {/* Bulk Review Modal */}
      <AdvertiserBulkReviewModal
        applications={selectedApplications}
        isOpen={bulkReviewModalOpen}
        onClose={() => setBulkReviewModalOpen(false)}
        onSubmit={handleBulkReviewSubmit}
      />
    </div>
  );
};

export default React.memo(AdvertiserApplicationList);
