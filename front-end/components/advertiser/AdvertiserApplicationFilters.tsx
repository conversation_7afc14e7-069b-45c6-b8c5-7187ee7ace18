'use client';
import React from 'react';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
}

interface AdvertiserApplicationFiltersProps {
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  applications: Application[];
  filteredCount?: number;
}

const AdvertiserApplicationFilters: React.FC<AdvertiserApplicationFiltersProps> = ({
  filters,
  onFilterChange,
  applications,
  filteredCount
}) => {
  // Calculate status counts
  const statusCounts = {
    total: applications.length,
    sp_approved: applications.filter(app => app.status === 'sp_approved').length,
    approved: applications.filter(app => app.status === 'approved').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  // Get unique campaigns for filter dropdown
  const uniqueCampaigns = Array.from(
    new Map(applications.map(app => [app.campaign.id, app.campaign])).values()
  );

  // Clear all filters
  const handleClearFilters = () => {
    onFilterChange({
      status: 'sp_approved',
      search: '',
      campaignId: 'all'
    });
  };

  return (
    <div className="panel p-6 space-y-6">
      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total</p>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{statusCounts.total}</p>
            </div>
            <div className="text-blue-500 text-2xl">📊</div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Awaiting Review</p>
              <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{statusCounts.sp_approved}</p>
            </div>
            <div className="text-yellow-500 text-2xl">⏳</div>
          </div>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600 dark:text-green-400">Approved</p>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{statusCounts.approved}</p>
            </div>
            <div className="text-green-500 text-2xl">✅</div>
          </div>
        </div>

        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600 dark:text-red-400">Rejected</p>
              <p className="text-2xl font-bold text-red-900 dark:text-red-100">{statusCounts.rejected}</p>
            </div>
            <div className="text-red-500 text-2xl">❌</div>
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Search */}
        <div className="lg:col-span-1">
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Applications
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              id="search"
              className="form-input pl-10"
              placeholder="Search by publisher email or campaign title..."
              value={filters.search}
              onChange={(e) => onFilterChange({ search: e.target.value })}
              aria-label="Search applications by publisher email or campaign title"
              aria-describedby="search-help"
            />
            <span id="search-help" className="sr-only">
              Search through applications by typing publisher email or campaign title
            </span>
          </div>
        </div>

        {/* Status Filter */}
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Status
          </label>
          <select
            id="status"
            className="form-select"
            value={filters.status}
            onChange={(e) => onFilterChange({ status: e.target.value })}
            aria-label="Filter applications by status"
            aria-describedby="status-help"
          >
            <option value="all">All Applications</option>
            <option value="sp_approved">Awaiting My Review</option>
            <option value="approved">Approved by Me</option>
            <option value="rejected">Rejected by Me</option>
          </select>
          <span id="status-help" className="sr-only">
            Filter applications by their current status in the review workflow
          </span>
        </div>

        {/* Campaign Filter */}
        <div>
          <label htmlFor="campaign" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Campaign
          </label>
          <select
            id="campaign"
            className="form-select"
            value={filters.campaignId}
            onChange={(e) => onFilterChange({ campaignId: e.target.value })}
            aria-label="Filter applications by campaign"
          >
            <option value="all">All Campaigns</option>
            {uniqueCampaigns.map(campaign => (
              <option key={campaign.id} value={campaign.id}>
                {campaign.title}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Filter Actions */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {filteredCount !== undefined && (
            <>Showing {filteredCount} of {applications.length} applications</>
          )}
        </div>
        <button
          onClick={handleClearFilters}
          className="btn btn-outline-secondary btn-sm"
          disabled={filters.status === 'sp_approved' && !filters.search && filters.campaignId === 'all'}
        >
          Clear Filters
        </button>
      </div>

      {/* Active Filters Display */}
      {(filters.status !== 'sp_approved' || filters.search || filters.campaignId !== 'all') && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Active filters:</span>
          
          {filters.status !== 'sp_approved' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Status: {filters.status === 'all' ? 'All' : 
                      filters.status === 'approved' ? 'Approved' : 
                      filters.status === 'rejected' ? 'Rejected' : 'Awaiting Review'}
              <button
                onClick={() => onFilterChange({ status: 'sp_approved' })}
                className="ml-1 text-blue-600 hover:text-blue-800"
                aria-label="Remove status filter"
              >
                ×
              </button>
            </span>
          )}
          
          {filters.search && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Search: "{filters.search}"
              <button
                onClick={() => onFilterChange({ search: '' })}
                className="ml-1 text-green-600 hover:text-green-800"
                aria-label="Remove search filter"
              >
                ×
              </button>
            </span>
          )}
          
          {filters.campaignId !== 'all' && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              Campaign: {uniqueCampaigns.find(c => c.id === filters.campaignId)?.title || 'Unknown'}
              <button
                onClick={() => onFilterChange({ campaignId: 'all' })}
                className="ml-1 text-purple-600 hover:text-purple-800"
                aria-label="Remove campaign filter"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default React.memo(AdvertiserApplicationFilters);
