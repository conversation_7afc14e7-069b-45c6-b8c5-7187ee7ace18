'use client';
import React, { useState, useRef, useCallback } from 'react';

interface VideoUploadComponentProps {
  onVideoSelect: (file: File) => void;
  onVideoRemove: () => void;
  selectedVideo: File | null;
  disabled?: boolean;
}

const VideoUploadComponent: React.FC<VideoUploadComponentProps> = ({
  onVideoSelect,
  onVideoRemove,
  selectedVideo,
  disabled = false
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Supported video formats
  const supportedFormats = ['video/mp4', 'video/mov', 'video/avi', 'video/webm'];
  const maxFileSize = 100 * 1024 * 1024; // 100MB

  // Validate video file
  const validateVideoFile = (file: File): string | null => {
    if (!supportedFormats.includes(file.type)) {
      return 'Please upload a valid video file (MP4, MOV, AVI, or WebM)';
    }
    
    if (file.size > maxFileSize) {
      return 'File size must be less than 100MB';
    }
    
    return null;
  };

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    const error = validateVideoFile(file);
    if (error) {
      alert(error);
      return;
    }

    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    
    onVideoSelect(file);
  }, [onVideoSelect]);

  // Handle drag events
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled, handleFileSelect]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // Handle remove video
  const handleRemoveVideo = useCallback(() => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setUploadProgress(0);
    onVideoRemove();
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [previewUrl, onVideoRemove]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration (placeholder - would need video metadata)
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {!selectedVideo ? (
        // Upload Area
        <div
          className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragOver
              ? 'border-primary bg-primary/5'
              : 'border-gray-300 dark:border-gray-600 hover:border-primary hover:bg-primary/5'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />
          
          <div className="space-y-4">
            <div className="text-6xl text-gray-400">🎬</div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Upload Your Video
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Drag and drop your video file here, or click to browse
              </p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>Supported formats: MP4, MOV, AVI, WebM</p>
                <p>Maximum file size: 100MB</p>
                <p>Recommended: 1080p resolution, 30-60 FPS</p>
              </div>
            </div>
            <button
              type="button"
              className="btn btn-primary"
              disabled={disabled}
            >
              Choose Video File
            </button>
          </div>
        </div>
      ) : (
        // Video Preview
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div className="flex items-start space-x-4">
            {/* Video Preview */}
            <div className="flex-shrink-0">
              {previewUrl ? (
                <video
                  src={previewUrl}
                  className="w-32 h-24 object-cover rounded-lg"
                  controls={false}
                  muted
                />
              ) : (
                <div className="w-32 h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <span className="text-2xl text-gray-400">🎬</span>
                </div>
              )}
            </div>
            
            {/* Video Info */}
            <div className="flex-1 min-w-0">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                {selectedVideo.name}
              </h4>
              <div className="mt-2 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <p>Size: {formatFileSize(selectedVideo.size)}</p>
                <p>Type: {selectedVideo.type}</p>
                <p>Last modified: {new Date(selectedVideo.lastModified).toLocaleDateString()}</p>
              </div>
              
              {/* Upload Progress */}
              {uploadProgress > 0 && uploadProgress < 100 && (
                <div className="mt-3">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600 dark:text-gray-400">Uploading...</span>
                    <span className="text-gray-600 dark:text-gray-400">{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex-shrink-0 flex space-x-2">
              <button
                type="button"
                onClick={() => previewUrl && window.open(previewUrl, '_blank')}
                className="btn btn-sm btn-outline-secondary"
                disabled={!previewUrl}
                title="Preview video"
              >
                👁️
              </button>
              <button
                type="button"
                onClick={handleRemoveVideo}
                className="btn btn-sm btn-outline-danger"
                disabled={disabled}
                title="Remove video"
              >
                🗑️
              </button>
            </div>
          </div>
          
          {/* Video Details */}
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h5 className="font-medium text-gray-900 dark:text-white mb-2">Video Details</h5>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">File Name:</span>
                <p className="font-medium truncate">{selectedVideo.name}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">File Size:</span>
                <p className="font-medium">{formatFileSize(selectedVideo.size)}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Format:</span>
                <p className="font-medium">{selectedVideo.type}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Duration:</span>
                <p className="font-medium">Calculating...</p>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Upload Tips */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <div className="text-blue-500 text-xl mr-3">💡</div>
          <div>
            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              Video Upload Tips
            </h5>
            <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
              <li>• Use high-quality video (1080p recommended)</li>
              <li>• Keep file size under 100MB for faster upload</li>
              <li>• Ensure good lighting and clear audio</li>
              <li>• Follow campaign guidelines and brand requirements</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(VideoUploadComponent);
