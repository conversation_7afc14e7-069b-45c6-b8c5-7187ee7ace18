'use client';
import React, { useState } from 'react';
import { DataTable } from 'mantine-datatable';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface VideoSubmissionListProps {
  submissions: VideoSubmission[];
  onResubmit: (submissionId: string) => void;
  onViewDetails: (submission: VideoSubmission) => void;
  loading: boolean;
}

const VideoSubmissionList: React.FC<VideoSubmissionListProps> = ({
  submissions,
  onResubmit,
  onViewDetails,
  loading
}) => {
  const [selectedSubmission, setSelectedSubmission] = useState<VideoSubmission | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Handle view details
  const handleViewDetails = (submission: VideoSubmission) => {
    setSelectedSubmission(submission);
    setDetailsModalOpen(true);
    onViewDetails(submission);
  };

  // Handle resubmit
  const handleResubmit = (submissionId: string) => {
    onResubmit(submissionId);
  };

  // Filter submissions for pagination
  const paginatedSubmissions = submissions.slice((page - 1) * pageSize, page * pageSize);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    const badges = {
      pending: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      sp_review: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      advertiser_review: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      approved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      rejected: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      published: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    };

    const labels = {
      pending: 'Pending',
      sp_review: 'SP Team Review',
      advertiser_review: 'Advertiser Review',
      approved: 'Approved',
      rejected: 'Rejected',
      published: 'Published'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badges[status as keyof typeof badges] || badges.pending}`}>
        {labels[status as keyof typeof labels] || status}
      </span>
    );
  };

  // Get submission type
  const getSubmissionType = (submission: VideoSubmission) => {
    if (submission.videoUrl) return 'Video Upload';
    if (submission.tiktokUrl) return 'TikTok Link';
    return 'Unknown';
  };

  // Table columns configuration
  const columns = [
    {
      accessor: 'campaign.title',
      title: 'Campaign',
      sortable: true,
      render: (submission: VideoSubmission) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {submission.campaign.title}
          </span>
          <span className="text-sm text-gray-500">ID: {submission.campaign.id}</span>
        </div>
      ),
    },
    {
      accessor: 'type',
      title: 'Type',
      render: (submission: VideoSubmission) => (
        <div className="flex items-center space-x-2">
          <span className="text-lg">
            {submission.videoUrl ? '🎬' : '📱'}
          </span>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {getSubmissionType(submission)}
          </span>
        </div>
      ),
    },
    {
      accessor: 'status',
      title: 'Status',
      sortable: true,
      render: (submission: VideoSubmission) => getStatusBadge(submission.status),
    },
    {
      accessor: 'submittedAt',
      title: 'Submitted',
      sortable: true,
      render: (submission: VideoSubmission) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(submission.submittedAt)}
        </div>
      ),
    },
    {
      accessor: 'updatedAt',
      title: 'Last Updated',
      sortable: true,
      render: (submission: VideoSubmission) => (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(submission.updatedAt)}
        </div>
      ),
    },
    {
      accessor: 'actions',
      title: 'Actions',
      textAlignment: 'center' as const,
      render: (submission: VideoSubmission) => (
        <div className="flex items-center justify-center space-x-2">
          <button
            onClick={() => handleViewDetails(submission)}
            className="btn btn-sm btn-outline-secondary"
            aria-label={`View details for ${submission.campaign.title}`}
          >
            View Details
          </button>
          {submission.status === 'rejected' && (
            <button
              onClick={() => handleResubmit(submission.id)}
              className="btn btn-sm btn-primary"
              aria-label={`Resubmit for ${submission.campaign.title}`}
            >
              Resubmit
            </button>
          )}
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="panel p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading submissions...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="panel">
      {/* Table Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Video Submissions ({submissions.length})
          </h2>
        </div>
        <div className="flex items-center space-x-2">
          <label htmlFor="page-size" className="text-sm text-gray-600 dark:text-gray-400">Show:</label>
          <select
            id="page-size"
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value));
              setPage(1);
            }}
            className="form-select form-select-sm w-20"
            aria-label="Number of submissions per page"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
          </select>
        </div>
      </div>

      {/* Data Table */}
      <div className="datatables">
        <DataTable
          className="whitespace-nowrap table-hover"
          records={paginatedSubmissions}
          columns={columns}
          totalRecords={submissions.length}
          recordsPerPage={pageSize}
          page={page}
          onPageChange={(p) => setPage(p)}
          recordsPerPageOptions={[10, 25, 50]}
          onRecordsPerPageChange={setPageSize}
          sortStatus={{ columnAccessor: 'submittedAt', direction: 'desc' }}
          minHeight={200}
          noRecordsText="No video submissions found"
          noRecordsIcon={
            <div className="flex flex-col items-center justify-center py-12">
              <div className="text-6xl text-gray-300 dark:text-gray-600 mb-4">🎬</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Video Submissions</h3>
              <p className="text-gray-500 dark:text-gray-400 text-center max-w-md">
                You haven't submitted any videos yet. Submit your first video using the "Submit New Video" tab.
              </p>
            </div>
          }
        />
      </div>

      {/* Details Modal */}
      {selectedSubmission && detailsModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Submission Details
              </h3>
              <button
                onClick={() => {
                  setDetailsModalOpen(false);
                  setSelectedSubmission(null);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                aria-label="Close modal"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Submission Overview */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Campaign Information</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <p className="font-medium text-gray-900 dark:text-white">
                      {selectedSubmission.campaign.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Advertiser: {selectedSubmission.campaign.advertiser.email}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Submission Details</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Type:</span>
                        <span className="text-sm font-medium">{getSubmissionType(selectedSubmission)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                        {getStatusBadge(selectedSubmission.status)}
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Submitted:</span>
                        <span className="text-sm font-medium">{formatDate(selectedSubmission.submittedAt)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Preview */}
              {selectedSubmission.videoUrl && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Video Preview</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Video file uploaded successfully
                    </p>
                  </div>
                </div>
              )}

              {selectedSubmission.tiktokUrl && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">TikTok Link</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <a
                      href={selectedSubmission.tiktokUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline text-sm break-all"
                    >
                      {selectedSubmission.tiktokUrl}
                    </a>
                  </div>
                </div>
              )}

              {/* Feedback */}
              {selectedSubmission.feedback && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feedback</h4>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                      {selectedSubmission.feedback}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
              {selectedSubmission.status === 'rejected' && (
                <button
                  onClick={() => {
                    handleResubmit(selectedSubmission.id);
                    setDetailsModalOpen(false);
                    setSelectedSubmission(null);
                  }}
                  className="btn btn-primary"
                >
                  Resubmit Video
                </button>
              )}
              <button
                onClick={() => {
                  setDetailsModalOpen(false);
                  setSelectedSubmission(null);
                }}
                className="btn btn-outline-secondary"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(VideoSubmissionList);
