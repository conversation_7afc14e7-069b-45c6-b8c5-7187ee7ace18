'use client';
import React, { useState, useCallback } from 'react';
import VideoUploadComponent from './VideoUploadComponent';
import TikTokLinkComponent from './TikTokLinkComponent';

interface Campaign {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  applicationStatus?: 'pending' | 'sp_approved' | 'approved' | 'rejected';
}

interface VideoSubmissionFormProps {
  campaigns: Campaign[];
  onSubmit: (campaignId: string, submissionData: { videoFile?: File; tiktokUrl?: string }) => Promise<void>;
  disabled?: boolean;
}

const VideoSubmissionForm: React.FC<VideoSubmissionFormProps> = ({
  campaigns,
  onSubmit,
  disabled = false
}) => {
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [submissionType, setSubmissionType] = useState<'video' | 'tiktok'>('video');
  const [selectedVideo, setSelectedVideo] = useState<File | null>(null);
  const [tiktokUrl, setTiktokUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Get approved campaigns
  const approvedCampaigns = campaigns.filter(campaign => 
    campaign.applicationStatus === 'approved' && campaign.status === 'active'
  );

  // Handle campaign selection
  const handleCampaignChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCampaign(e.target.value);
  }, []);

  // Handle submission type change
  const handleSubmissionTypeChange = useCallback((type: 'video' | 'tiktok') => {
    setSubmissionType(type);
    // Clear previous selections when switching types
    if (type === 'video') {
      setTiktokUrl('');
    } else {
      setSelectedVideo(null);
    }
  }, []);

  // Handle video selection
  const handleVideoSelect = useCallback((file: File) => {
    setSelectedVideo(file);
  }, []);

  // Handle video removal
  const handleVideoRemove = useCallback(() => {
    setSelectedVideo(null);
  }, []);

  // Handle TikTok URL change
  const handleTikTokUrlChange = useCallback((url: string) => {
    setTiktokUrl(url);
  }, []);

  // Validate form
  const isFormValid = (): boolean => {
    if (!selectedCampaign) return false;
    
    if (submissionType === 'video') {
      return selectedVideo !== null;
    } else {
      return tiktokUrl.trim() !== '';
    }
  };

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFormValid() || isSubmitting) return;

    setShowConfirmation(true);
  }, [isFormValid, isSubmitting]);

  // Handle confirmed submission
  const handleConfirmedSubmit = useCallback(async () => {
    if (!selectedCampaign || isSubmitting) return;

    setIsSubmitting(true);
    setShowConfirmation(false);

    try {
      const submissionData: { videoFile?: File; tiktokUrl?: string } = {};
      
      if (submissionType === 'video' && selectedVideo) {
        submissionData.videoFile = selectedVideo;
      } else if (submissionType === 'tiktok' && tiktokUrl) {
        submissionData.tiktokUrl = tiktokUrl;
      }

      await onSubmit(selectedCampaign, submissionData);
      
      // Reset form on success
      setSelectedCampaign('');
      setSelectedVideo(null);
      setTiktokUrl('');
      setSubmissionType('video');
      
      alert('Video submitted successfully!');
    } catch (error) {
      console.error('Submission failed:', error);
      alert('Failed to submit video. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedCampaign, submissionType, selectedVideo, tiktokUrl, onSubmit, isSubmitting]);

  // Handle cancel confirmation
  const handleCancelConfirmation = useCallback(() => {
    setShowConfirmation(false);
  }, []);

  // Get selected campaign details
  const selectedCampaignDetails = approvedCampaigns.find(c => c.id === selectedCampaign);

  return (
    <div className="space-y-6">
      {/* Campaign Selection */}
      <div>
        <label htmlFor="campaign" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Campaign <span className="text-red-500">*</span>
        </label>
        <select
          id="campaign"
          value={selectedCampaign}
          onChange={handleCampaignChange}
          className="form-select w-full"
          disabled={disabled || isSubmitting}
          required
        >
          <option value="">Choose a campaign...</option>
          {approvedCampaigns.map(campaign => (
            <option key={campaign.id} value={campaign.id}>
              {campaign.title}
            </option>
          ))}
        </select>
        {approvedCampaigns.length === 0 && (
          <p className="mt-1 text-sm text-gray-500">
            No approved campaigns available. Apply to campaigns first.
          </p>
        )}
      </div>

      {/* Campaign Details */}
      {selectedCampaignDetails && (
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            {selectedCampaignDetails.title}
          </h4>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            {selectedCampaignDetails.description}
          </p>
        </div>
      )}

      {/* Submission Type Selection */}
      {selectedCampaign && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Submission Type <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 gap-4">
            <button
              type="button"
              onClick={() => handleSubmissionTypeChange('video')}
              className={`p-4 border-2 rounded-lg text-left transition-colors ${
                submissionType === 'video'
                  ? 'border-primary bg-primary/5 text-primary'
                  : 'border-gray-200 dark:border-gray-700 hover:border-primary hover:bg-primary/5'
              }`}
              disabled={disabled || isSubmitting}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🎬</div>
                <div>
                  <h5 className="font-medium">Upload Video</h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Upload a video file from your device
                  </p>
                </div>
              </div>
            </button>
            
            <button
              type="button"
              onClick={() => handleSubmissionTypeChange('tiktok')}
              className={`p-4 border-2 rounded-lg text-left transition-colors ${
                submissionType === 'tiktok'
                  ? 'border-primary bg-primary/5 text-primary'
                  : 'border-gray-200 dark:border-gray-700 hover:border-primary hover:bg-primary/5'
              }`}
              disabled={disabled || isSubmitting}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">📱</div>
                <div>
                  <h5 className="font-medium">TikTok Link</h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Submit a link to your TikTok video
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Video Upload Section */}
      {selectedCampaign && submissionType === 'video' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Upload Your Video
          </h4>
          <VideoUploadComponent
            onVideoSelect={handleVideoSelect}
            onVideoRemove={handleVideoRemove}
            selectedVideo={selectedVideo}
            disabled={disabled || isSubmitting}
          />
        </div>
      )}

      {/* TikTok Link Section */}
      {selectedCampaign && submissionType === 'tiktok' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Submit TikTok Link
          </h4>
          <TikTokLinkComponent
            onTikTokUrlChange={handleTikTokUrlChange}
            tiktokUrl={tiktokUrl}
            disabled={disabled || isSubmitting}
          />
        </div>
      )}

      {/* Submit Button */}
      {selectedCampaign && (
        <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={() => {
              setSelectedCampaign('');
              setSelectedVideo(null);
              setTiktokUrl('');
              setSubmissionType('video');
            }}
            className="btn btn-outline-secondary"
            disabled={isSubmitting}
          >
            Reset Form
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={!isFormValid() || isSubmitting}
            className="btn btn-primary"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </div>
            ) : (
              'Submit Video'
            )}
          </button>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Confirm Video Submission</h3>
            <div className="space-y-3 mb-6">
              <p><strong>Campaign:</strong> {selectedCampaignDetails?.title}</p>
              <p><strong>Submission Type:</strong> {submissionType === 'video' ? 'Video Upload' : 'TikTok Link'}</p>
              {submissionType === 'video' && selectedVideo && (
                <p><strong>File:</strong> {selectedVideo.name}</p>
              )}
              {submissionType === 'tiktok' && tiktokUrl && (
                <p><strong>TikTok URL:</strong> {tiktokUrl}</p>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={handleConfirmedSubmit}
                className="btn btn-primary flex-1"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Confirm Submit'}
              </button>
              <button
                onClick={handleCancelConfirmation}
                className="btn btn-outline-secondary flex-1"
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoSubmissionForm;
