'use client';
import React, { useState, useCallback, useEffect } from 'react';

interface TikTokLinkComponentProps {
  onTikTokUrlChange: (url: string) => void;
  tiktokUrl: string;
  disabled?: boolean;
}

interface TikTokVideoInfo {
  id: string;
  title: string;
  thumbnail: string;
  duration: number;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  author: {
    username: string;
    displayName: string;
    avatar: string;
  };
}

const TikTokLinkComponent: React.FC<TikTokLinkComponentProps> = ({
  onTikTokUrlChange,
  tiktokUrl,
  disabled = false
}) => {
  const [inputUrl, setInputUrl] = useState(tiktokUrl);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [videoInfo, setVideoInfo] = useState<TikTokVideoInfo | null>(null);

  // TikTok URL patterns
  const tiktokUrlPatterns = [
    /^https?:\/\/(www\.)?tiktok\.com\/@[\w.-]+\/video\/\d+/,
    /^https?:\/\/vm\.tiktok\.com\/[\w]+/,
    /^https?:\/\/www\.tiktok\.com\/t\/[\w]+/
  ];

  // Validate TikTok URL
  const validateTikTokUrl = useCallback((url: string): boolean => {
    if (!url.trim()) return false;
    return tiktokUrlPatterns.some(pattern => pattern.test(url));
  }, []);

  // Extract video ID from TikTok URL (simplified)
  const extractVideoId = useCallback((url: string): string | null => {
    const match = url.match(/\/video\/(\d+)/);
    return match ? match[1] : null;
  }, []);

  // Mock function to fetch TikTok video info
  const fetchTikTokVideoInfo = useCallback(async (url: string): Promise<TikTokVideoInfo | null> => {
    // In a real app, this would call your backend API to fetch TikTok video info
    // For now, return mock data
    const videoId = extractVideoId(url);
    if (!videoId) return null;

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      id: videoId,
      title: 'Sample TikTok Video Title',
      thumbnail: 'https://via.placeholder.com/300x400/ff0050/ffffff?text=TikTok',
      duration: 30,
      viewCount: 125000,
      likeCount: 8500,
      shareCount: 1200,
      author: {
        username: 'sample_creator',
        displayName: 'Sample Creator',
        avatar: 'https://via.placeholder.com/100x100/ff0050/ffffff?text=TC'
      }
    };
  }, [extractVideoId]);

  // Handle URL input change
  const handleUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setInputUrl(url);
    setValidationError(null);
    setVideoInfo(null);
  }, []);

  // Handle URL validation and submission
  const handleUrlSubmit = useCallback(async () => {
    const url = inputUrl.trim();
    
    if (!url) {
      setValidationError('Please enter a TikTok URL');
      return;
    }

    if (!validateTikTokUrl(url)) {
      setValidationError('Please enter a valid TikTok URL');
      return;
    }

    setIsValidating(true);
    setValidationError(null);

    try {
      const info = await fetchTikTokVideoInfo(url);
      if (info) {
        setVideoInfo(info);
        onTikTokUrlChange(url);
      } else {
        setValidationError('Could not fetch video information. Please check the URL.');
      }
    } catch (error) {
      setValidationError('Failed to validate TikTok URL. Please try again.');
    } finally {
      setIsValidating(false);
    }
  }, [inputUrl, validateTikTokUrl, fetchTikTokVideoInfo, onTikTokUrlChange]);

  // Handle URL removal
  const handleRemoveUrl = useCallback(() => {
    setInputUrl('');
    setVideoInfo(null);
    setValidationError(null);
    onTikTokUrlChange('');
  }, [onTikTokUrlChange]);

  // Handle Enter key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !disabled && !isValidating) {
      handleUrlSubmit();
    }
  }, [disabled, isValidating, handleUrlSubmit]);

  // Format numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Format duration
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* URL Input */}
      <div>
        <label htmlFor="tiktok-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          TikTok Video URL
        </label>
        <div className="flex space-x-2">
          <div className="flex-1">
            <input
              id="tiktok-url"
              type="url"
              value={inputUrl}
              onChange={handleUrlChange}
              onKeyPress={handleKeyPress}
              placeholder="https://www.tiktok.com/@username/video/1234567890"
              className={`form-input w-full ${
                validationError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              disabled={disabled || isValidating}
            />
            {validationError && (
              <p className="mt-1 text-sm text-red-600">{validationError}</p>
            )}
          </div>
          <button
            type="button"
            onClick={handleUrlSubmit}
            disabled={disabled || isValidating || !inputUrl.trim()}
            className="btn btn-primary"
          >
            {isValidating ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Validating...</span>
              </div>
            ) : (
              'Validate'
            )}
          </button>
        </div>
      </div>

      {/* Video Preview */}
      {videoInfo && (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div className="flex items-start space-x-4">
            {/* Thumbnail */}
            <div className="flex-shrink-0">
              <img
                src={videoInfo.thumbnail}
                alt="TikTok video thumbnail"
                className="w-24 h-32 object-cover rounded-lg"
              />
              <div className="mt-2 text-center">
                <span className="text-xs bg-black text-white px-2 py-1 rounded">
                  {formatDuration(videoInfo.duration)}
                </span>
              </div>
            </div>
            
            {/* Video Info */}
            <div className="flex-1 min-w-0">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {videoInfo.title}
              </h4>
              
              {/* Author Info */}
              <div className="flex items-center space-x-2 mb-3">
                <img
                  src={videoInfo.author.avatar}
                  alt={videoInfo.author.displayName}
                  className="w-8 h-8 rounded-full"
                />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {videoInfo.author.displayName}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    @{videoInfo.author.username}
                  </p>
                </div>
              </div>
              
              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {formatNumber(videoInfo.viewCount)}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">Views</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {formatNumber(videoInfo.likeCount)}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">Likes</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {formatNumber(videoInfo.shareCount)}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">Shares</p>
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex-shrink-0 flex space-x-2">
              <button
                type="button"
                onClick={() => window.open(inputUrl, '_blank')}
                className="btn btn-sm btn-outline-secondary"
                title="View on TikTok"
              >
                🔗
              </button>
              <button
                type="button"
                onClick={handleRemoveUrl}
                className="btn btn-sm btn-outline-danger"
                disabled={disabled}
                title="Remove URL"
              >
                🗑️
              </button>
            </div>
          </div>
        </div>
      )}

      {/* URL Format Examples */}
      <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <h5 className="font-medium text-gray-900 dark:text-white mb-2">Supported URL Formats</h5>
        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <p>• https://www.tiktok.com/@username/video/1234567890</p>
          <p>• https://vm.tiktok.com/ZMexample/</p>
          <p>• https://www.tiktok.com/t/ZTexample/</p>
        </div>
      </div>

      {/* TikTok Integration Tips */}
      <div className="bg-pink-50 dark:bg-pink-900/20 p-4 rounded-lg border border-pink-200 dark:border-pink-800">
        <div className="flex items-start">
          <div className="text-pink-500 text-xl mr-3">📱</div>
          <div>
            <h5 className="font-medium text-pink-900 dark:text-pink-100 mb-1">
              TikTok Submission Tips
            </h5>
            <ul className="text-pink-700 dark:text-pink-300 text-sm space-y-1">
              <li>• Make sure your TikTok video is public</li>
              <li>• Include campaign hashtags and mentions</li>
              <li>• Follow TikTok community guidelines</li>
              <li>• Ensure video aligns with campaign requirements</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(TikTokLinkComponent);
