'use client';
import React, { useState, useEffect } from 'react';

interface Campaign {
  id?: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
  budget?: number;
  requirements?: string;
  targetAudience?: string;
  contentGuidelines?: string;
  deliverables?: string[];
  applicationDeadline?: string;
}

interface Advertiser {
  id: string;
  name: string;
  email: string;
}

interface CampaignFormProps {
  campaign?: Campaign;
  onSubmit: (campaignData: Omit<Campaign, 'id'>) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
  loading?: boolean;
}

const CampaignForm: React.FC<CampaignFormProps> = ({
  campaign,
  onSubmit,
  onCancel,
  isEditing = false,
  loading = false
}) => {
  // Form state
  const [formData, setFormData] = useState<Omit<Campaign, 'id'>>({
    title: '',
    description: '',
    advertiserId: '',
    advertiser: {
      id: '',
      name: '',
      email: ''
    },
    startDate: '',
    endDate: '',
    status: 'draft',
    assignedPublisherIds: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    budget: 0,
    requirements: '',
    targetAudience: '',
    contentGuidelines: '',
    deliverables: [],
    applicationDeadline: ''
  });

  const [advertisers, setAdvertisers] = useState<Advertiser[]>([]);
  const [loadingAdvertisers, setLoadingAdvertisers] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [deliverableInput, setDeliverableInput] = useState('');

  // Initialize form data when editing
  useEffect(() => {
    if (campaign && isEditing) {
      setFormData({
        title: campaign.title || '',
        description: campaign.description || '',
        advertiserId: campaign.advertiserId || '',
        advertiser: campaign.advertiser || {
          id: campaign.advertiserId || '',
          name: 'Unknown Advertiser',
          email: '<EMAIL>'
        },
        startDate: campaign.startDate ? campaign.startDate.split('T')[0] : '',
        endDate: campaign.endDate ? campaign.endDate.split('T')[0] : '',
        status: campaign.status || 'draft',
        assignedPublisherIds: campaign.assignedPublisherIds || [],
        createdAt: campaign.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        budget: campaign.budget || 0,
        requirements: campaign.requirements || '',
        targetAudience: campaign.targetAudience || '',
        contentGuidelines: campaign.contentGuidelines || '',
        deliverables: campaign.deliverables || [],
        applicationDeadline: campaign.applicationDeadline ? campaign.applicationDeadline.split('T')[0] : ''
      });
    }
  }, [campaign, isEditing]);

  // Fetch advertisers
  useEffect(() => {
    const fetchAdvertisers = async () => {
      try {
        const response = await fetch('http://localhost:3000/user?role=advertiser', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          const advertiserUsers = Array.isArray(data) ? data.map((user: any) => ({
            id: user.id,
            name: user.name || user.email,
            email: user.email
          })) : [];
          setAdvertisers(advertiserUsers);
        }
      } catch (error) {
        console.error('Failed to fetch advertisers:', error);
        // Mock data for development
        setAdvertisers([
          { id: 'adv-1', name: 'Tech Corp', email: '<EMAIL>' },
          { id: 'adv-2', name: 'Fashion Brand', email: '<EMAIL>' },
          { id: 'adv-3', name: 'Food Company', email: '<EMAIL>' }
        ]);
      } finally {
        setLoadingAdvertisers(false);
      }
    };

    fetchAdvertisers();
  }, []);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'advertiserId') {
      const selectedAdvertiser = advertisers.find(adv => adv.id === value);
      setFormData(prev => ({
        ...prev,
        [name]: value,
        advertiser: selectedAdvertiser || {
          id: value,
          name: 'Unknown Advertiser',
          email: '<EMAIL>'
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle deliverable addition
  const handleAddDeliverable = () => {
    if (deliverableInput.trim()) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...(prev.deliverables || []), deliverableInput.trim()]
      }));
      setDeliverableInput('');
    }
  };

  // Handle deliverable removal
  const handleRemoveDeliverable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables?.filter((_, i) => i !== index) || []
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Campaign title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Campaign description is required';
    }

    if (!formData.advertiserId) {
      newErrors.advertiserId = 'Please select an advertiser';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'End date is required';
    }

    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (formData.applicationDeadline && formData.startDate && new Date(formData.applicationDeadline) >= new Date(formData.startDate)) {
      newErrors.applicationDeadline = 'Application deadline must be before start date';
    }

    if (formData.budget && formData.budget < 0) {
      newErrors.budget = 'Budget must be a positive number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  return (
    <div className="panel">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {isEditing ? 'Edit Campaign' : 'Create New Campaign'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          disabled={loading}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Campaign Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`form-input ${errors.title ? 'border-red-500' : ''}`}
              placeholder="Enter campaign title"
              disabled={loading}
              required
            />
            {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
          </div>

          <div>
            <label htmlFor="advertiserId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Advertiser *
            </label>
            <select
              id="advertiserId"
              name="advertiserId"
              value={formData.advertiserId}
              onChange={handleChange}
              className={`form-select ${errors.advertiserId ? 'border-red-500' : ''}`}
              disabled={loading || loadingAdvertisers}
              required
            >
              <option value="">Select an advertiser</option>
              {advertisers.map(advertiser => (
                <option key={advertiser.id} value={advertiser.id}>
                  {advertiser.name} ({advertiser.email})
                </option>
              ))}
            </select>
            {errors.advertiserId && <p className="mt-1 text-sm text-red-600">{errors.advertiserId}</p>}
          </div>
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Campaign Description *
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            className={`form-textarea ${errors.description ? 'border-red-500' : ''}`}
            placeholder="Describe the campaign objectives, requirements, and expectations"
            disabled={loading}
            required
          />
          {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
        </div>

        {/* Dates and Budget */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Start Date *
            </label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              className={`form-input ${errors.startDate ? 'border-red-500' : ''}`}
              disabled={loading}
              required
            />
            {errors.startDate && <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>}
          </div>

          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              End Date *
            </label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              value={formData.endDate}
              onChange={handleChange}
              className={`form-input ${errors.endDate ? 'border-red-500' : ''}`}
              disabled={loading}
              required
            />
            {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>}
          </div>

          <div>
            <label htmlFor="applicationDeadline" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Application Deadline
            </label>
            <input
              type="date"
              id="applicationDeadline"
              name="applicationDeadline"
              value={formData.applicationDeadline}
              onChange={handleChange}
              className={`form-input ${errors.applicationDeadline ? 'border-red-500' : ''}`}
              disabled={loading}
            />
            {errors.applicationDeadline && <p className="mt-1 text-sm text-red-600">{errors.applicationDeadline}</p>}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label htmlFor="budget" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Budget ($)
            </label>
            <input
              type="number"
              id="budget"
              name="budget"
              value={formData.budget}
              onChange={handleChange}
              className={`form-input ${errors.budget ? 'border-red-500' : ''}`}
              placeholder="0"
              min="0"
              step="0.01"
              disabled={loading}
            />
            {errors.budget && <p className="mt-1 text-sm text-red-600">{errors.budget}</p>}
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="form-select"
              disabled={loading}
            >
              <option value="draft">Draft</option>
              <option value="active">Active</option>
              <option value="paused">Paused</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {/* Additional Details */}
        <div className="space-y-6">
          <div>
            <label htmlFor="requirements" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Requirements
            </label>
            <textarea
              id="requirements"
              name="requirements"
              value={formData.requirements}
              onChange={handleChange}
              rows={3}
              className="form-textarea"
              placeholder="Specify any requirements for publishers (follower count, engagement rate, etc.)"
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Target Audience
            </label>
            <textarea
              id="targetAudience"
              name="targetAudience"
              value={formData.targetAudience}
              onChange={handleChange}
              rows={3}
              className="form-textarea"
              placeholder="Describe the target audience for this campaign"
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="contentGuidelines" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Content Guidelines
            </label>
            <textarea
              id="contentGuidelines"
              name="contentGuidelines"
              value={formData.contentGuidelines}
              onChange={handleChange}
              rows={3}
              className="form-textarea"
              placeholder="Provide guidelines for content creation"
              disabled={loading}
            />
          </div>

          {/* Deliverables */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Deliverables
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={deliverableInput}
                onChange={(e) => setDeliverableInput(e.target.value)}
                className="form-input flex-1"
                placeholder="Add a deliverable"
                disabled={loading}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddDeliverable())}
              />
              <button
                type="button"
                onClick={handleAddDeliverable}
                className="btn btn-outline-primary"
                disabled={loading || !deliverableInput.trim()}
              >
                Add
              </button>
            </div>
            {formData.deliverables && formData.deliverables.length > 0 && (
              <div className="space-y-2">
                {formData.deliverables.map((deliverable, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                    <span className="text-sm">{deliverable}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveDeliverable(index)}
                      className="text-red-500 hover:text-red-700"
                      disabled={loading}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-outline-secondary"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>{isEditing ? 'Updating...' : 'Creating...'}</span>
              </div>
            ) : (
              isEditing ? 'Update Campaign' : 'Create Campaign'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default React.memo(CampaignForm);
