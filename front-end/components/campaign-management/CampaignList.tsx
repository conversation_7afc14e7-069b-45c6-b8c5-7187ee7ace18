'use client';
import React, { useState } from 'react';

interface Campaign {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
  budget?: number;
  requirements?: string;
  targetAudience?: string;
  contentGuidelines?: string;
  deliverables?: string[];
  applicationDeadline?: string;
  publisherAssignments?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    assignedAt: string;
    isActive: boolean;
  }[];
  applications?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
    appliedAt: string;
  }[];
  videoSubmissions?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
    submittedAt: string;
  }[];
}

interface CampaignListProps {
  campaigns: Campaign[];
  onEdit: (campaign: Campaign) => void;
  onDelete: (campaignId: string) => void;
  onViewDetails: (campaign: Campaign) => void;
  onAssignPublishers: (campaign: Campaign) => void;
  onViewAnalytics: (campaign: Campaign) => void;
  loading?: boolean;
}

const CampaignList: React.FC<CampaignListProps> = ({
  campaigns,
  onEdit,
  onDelete,
  onViewDetails,
  onAssignPublishers,
  onViewAnalytics,
  loading = false
}) => {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [campaignToDelete, setCampaignToDelete] = useState<Campaign | null>(null);

  // Get status badge color
  const getStatusBadge = (status: Campaign['status']) => {
    const badges = {
      draft: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      paused: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    return badges[status] || badges.draft;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle delete confirmation
  const handleDeleteClick = (campaign: Campaign) => {
    setCampaignToDelete(campaign);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = () => {
    if (campaignToDelete) {
      onDelete(campaignToDelete.id);
      setShowDeleteModal(false);
      setCampaignToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setCampaignToDelete(null);
  };

  // Calculate campaign metrics
  const getCampaignMetrics = (campaign: Campaign) => {
    const totalApplications = campaign.applications?.length || 0;
    const approvedApplications = campaign.applications?.filter(app => app.status === 'approved').length || 0;
    const assignedPublishers = campaign.publisherAssignments?.filter(pa => pa.isActive).length || 0;
    const videoSubmissions = campaign.videoSubmissions?.length || 0;
    const publishedVideos = campaign.videoSubmissions?.filter(vs => vs.status === 'published').length || 0;

    return {
      totalApplications,
      approvedApplications,
      assignedPublishers,
      videoSubmissions,
      publishedVideos
    };
  };

  if (campaigns.length === 0) {
    return (
      <div className="panel p-12">
        <div className="text-center text-gray-500">
          <div className="text-6xl mb-4">📋</div>
          <h3 className="text-lg font-medium mb-2">No Campaigns Found</h3>
          <p>Create your first campaign to get started with publisher management.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {campaigns.map((campaign) => {
          const metrics = getCampaignMetrics(campaign);
          const isExpired = new Date(campaign.endDate) < new Date();
          const daysUntilEnd = Math.ceil((new Date(campaign.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

          return (
            <div key={campaign.id} className="panel p-6 hover:shadow-lg transition-shadow">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                {/* Campaign Info */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                        {campaign.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        by {campaign.advertiser.name}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(campaign.status)}`}>
                      {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                    </span>
                  </div>

                  <p className="text-gray-700 dark:text-gray-300 mb-4 line-clamp-2">
                    {campaign.description}
                  </p>

                  {/* Campaign Dates */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div className="flex items-center space-x-1">
                      <span>📅</span>
                      <span>{formatDate(campaign.startDate)} - {formatDate(campaign.endDate)}</span>
                    </div>
                    {campaign.budget && (
                      <div className="flex items-center space-x-1">
                        <span>💰</span>
                        <span>${campaign.budget.toLocaleString()}</span>
                      </div>
                    )}
                    {!isExpired && daysUntilEnd > 0 && (
                      <div className="flex items-center space-x-1">
                        <span>⏰</span>
                        <span>{daysUntilEnd} days remaining</span>
                      </div>
                    )}
                    {isExpired && (
                      <div className="flex items-center space-x-1 text-red-500">
                        <span>⚠️</span>
                        <span>Expired</span>
                      </div>
                    )}
                  </div>

                  {/* Campaign Metrics */}
                  <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {metrics.assignedPublishers}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Assigned Publishers
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {metrics.totalApplications}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Applications
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {metrics.approvedApplications}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Approved
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {metrics.videoSubmissions}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Videos Submitted
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {metrics.publishedVideos}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Published
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col sm:flex-row gap-2 lg:flex-col lg:w-48">
                  <button
                    onClick={() => onViewDetails(campaign)}
                    className="btn btn-outline-primary btn-sm"
                    disabled={loading}
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => onAssignPublishers(campaign)}
                    className="btn btn-outline-secondary btn-sm"
                    disabled={loading}
                  >
                    Assign Publishers
                  </button>
                  <button
                    onClick={() => onEdit(campaign)}
                    className="btn btn-outline-info btn-sm"
                    disabled={loading}
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteClick(campaign)}
                    className="btn btn-outline-danger btn-sm"
                    disabled={loading || campaign.status === 'active'}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && campaignToDelete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Delete Campaign
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Are you sure you want to delete "{campaignToDelete.title}"? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={handleDeleteCancel}
                    className="btn btn-outline-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDeleteConfirm}
                    className="btn btn-danger"
                  >
                    Delete Campaign
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default React.memo(CampaignList);
