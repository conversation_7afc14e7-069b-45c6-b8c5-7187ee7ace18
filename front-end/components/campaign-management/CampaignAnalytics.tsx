'use client';
import React, { useState, useEffect } from 'react';

interface Campaign {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
  budget?: number;
  requirements?: string;
  targetAudience?: string;
  contentGuidelines?: string;
  deliverables?: string[];
  applicationDeadline?: string;
}

interface CampaignMetrics {
  totalApplications: number;
  approvedApplications: number;
  rejectedApplications: number;
  pendingApplications: number;
  assignedPublishers: number;
  videoSubmissions: number;
  approvedVideos: number;
  rejectedVideos: number;
  publishedVideos: number;
  totalViews: number;
  totalEngagement: number;
  averageEngagementRate: number;
  conversionRate: number;
  costPerEngagement: number;
  roi: number;
}

interface PublisherPerformance {
  id: string;
  name: string;
  email: string;
  username: string;
  videosSubmitted: number;
  videosApproved: number;
  videosPublished: number;
  totalViews: number;
  totalEngagement: number;
  engagementRate: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

interface CampaignAnalyticsProps {
  campaign: Campaign;
  onClose: () => void;
}

const CampaignAnalytics: React.FC<CampaignAnalyticsProps> = ({
  campaign,
  onClose
}) => {
  const [metrics, setMetrics] = useState<CampaignMetrics | null>(null);
  const [publisherPerformance, setPublisherPerformance] = useState<PublisherPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'publishers' | 'content'>('overview');

  // Fetch campaign analytics
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setError(null);
        
        // In a real app, these would be separate API calls
        const [metricsResponse, performanceResponse] = await Promise.all([
          fetch(`http://localhost:3000/campaign/${campaign.id}/metrics`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json',
            },
          }),
          fetch(`http://localhost:3000/campaign/${campaign.id}/publisher-performance`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json',
            },
          })
        ]);

        // Mock data for development (replace with real API responses)
        const mockMetrics: CampaignMetrics = {
          totalApplications: Math.floor(Math.random() * 100) + 20,
          approvedApplications: Math.floor(Math.random() * 50) + 10,
          rejectedApplications: Math.floor(Math.random() * 20) + 5,
          pendingApplications: Math.floor(Math.random() * 30) + 5,
          assignedPublishers: campaign.assignedPublisherIds.length,
          videoSubmissions: Math.floor(Math.random() * 40) + 10,
          approvedVideos: Math.floor(Math.random() * 30) + 5,
          rejectedVideos: Math.floor(Math.random() * 10) + 2,
          publishedVideos: Math.floor(Math.random() * 25) + 3,
          totalViews: Math.floor(Math.random() * 1000000) + 100000,
          totalEngagement: Math.floor(Math.random() * 50000) + 10000,
          averageEngagementRate: Math.floor(Math.random() * 8) + 2,
          conversionRate: Math.floor(Math.random() * 5) + 1,
          costPerEngagement: Math.floor(Math.random() * 2) + 0.5,
          roi: Math.floor(Math.random() * 300) + 150
        };

        const mockPublisherPerformance: PublisherPerformance[] = campaign.assignedPublisherIds.map((id, index) => ({
          id,
          name: `Publisher ${index + 1}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`,
          videosSubmitted: Math.floor(Math.random() * 5) + 1,
          videosApproved: Math.floor(Math.random() * 4) + 1,
          videosPublished: Math.floor(Math.random() * 3) + 1,
          totalViews: Math.floor(Math.random() * 100000) + 10000,
          totalEngagement: Math.floor(Math.random() * 5000) + 1000,
          engagementRate: Math.floor(Math.random() * 10) + 2,
          performance: ['excellent', 'good', 'average', 'poor'][Math.floor(Math.random() * 4)] as any
        }));

        setMetrics(mockMetrics);
        setPublisherPerformance(mockPublisherPerformance);
      } catch (err) {
        console.error('Failed to fetch analytics:', err);
        setError('Failed to load campaign analytics');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [campaign.id, campaign.assignedPublisherIds]);

  // Calculate campaign progress
  const getCampaignProgress = () => {
    const now = new Date();
    const start = new Date(campaign.startDate);
    const end = new Date(campaign.endDate);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();
    return Math.floor((elapsed / total) * 100);
  };

  // Get performance badge
  const getPerformanceBadge = (performance: string) => {
    const badges = {
      excellent: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      good: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      average: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      poor: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    };
    return badges[performance as keyof typeof badges] || badges.average;
  };

  const progress = getCampaignProgress();

  if (loading) {
    return (
      <div className="panel">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <div className="panel">
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">📊</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Analytics Unavailable</h3>
          <p className="text-gray-600 dark:text-gray-400">{error || 'Unable to load campaign analytics'}</p>
          <button onClick={onClose} className="btn btn-primary mt-4">
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="panel">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Campaign Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {campaign.title}
          </p>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Campaign Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Campaign Progress</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300" 
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>{new Date(campaign.startDate).toLocaleDateString()}</span>
          <span>{new Date(campaign.endDate).toLocaleDateString()}</span>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setSelectedTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'overview'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedTab('publishers')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'publishers'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Publisher Performance
          </button>
          <button
            onClick={() => setSelectedTab('content')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'content'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Content Performance
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {selectedTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{metrics.totalApplications}</div>
                <div className="text-sm text-blue-600 dark:text-blue-400">Total Applications</div>
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">{metrics.publishedVideos}</div>
                <div className="text-sm text-green-600 dark:text-green-400">Published Videos</div>
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">{metrics.totalViews.toLocaleString()}</div>
                <div className="text-sm text-purple-600 dark:text-purple-400">Total Views</div>
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">{metrics.averageEngagementRate}%</div>
                <div className="text-sm text-orange-600 dark:text-orange-400">Avg Engagement</div>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Application Funnel</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Total Applications</span>
                  <span className="font-medium">{metrics.totalApplications}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Approved</span>
                  <span className="font-medium text-green-600">{metrics.approvedApplications}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Rejected</span>
                  <span className="font-medium text-red-600">{metrics.rejectedApplications}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                  <span className="font-medium text-yellow-600">{metrics.pendingApplications}</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Content Pipeline</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Videos Submitted</span>
                  <span className="font-medium">{metrics.videoSubmissions}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Approved</span>
                  <span className="font-medium text-green-600">{metrics.approvedVideos}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Rejected</span>
                  <span className="font-medium text-red-600">{metrics.rejectedVideos}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Published</span>
                  <span className="font-medium text-blue-600">{metrics.publishedVideos}</span>
                </div>
              </div>
            </div>
          </div>

          {/* ROI Metrics */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Return on Investment</h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{metrics.roi}%</div>
                <div className="text-sm text-blue-600 dark:text-blue-400">ROI</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">${metrics.costPerEngagement.toFixed(2)}</div>
                <div className="text-sm text-purple-600 dark:text-purple-400">Cost per Engagement</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">{metrics.conversionRate}%</div>
                <div className="text-sm text-green-600 dark:text-green-400">Conversion Rate</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'publishers' && (
        <div className="space-y-4">
          {publisherPerformance.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-6xl mb-4">👥</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Publisher Data</h3>
              <p className="text-gray-600 dark:text-gray-400">No publishers have been assigned to this campaign yet.</p>
            </div>
          ) : (
            publisherPerformance.map((publisher) => (
              <div key={publisher.id} className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{publisher.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">@{publisher.username}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceBadge(publisher.performance)}`}>
                    {publisher.performance.charAt(0).toUpperCase() + publisher.performance.slice(1)}
                  </span>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{publisher.videosSubmitted}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Submitted</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{publisher.videosApproved}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Approved</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{publisher.videosPublished}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Published</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{publisher.totalViews.toLocaleString()}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Views</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{publisher.engagementRate}%</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Engagement</div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {selectedTab === 'content' && (
        <div className="text-center py-12">
          <div className="text-gray-500 text-6xl mb-4">📹</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Content Performance</h3>
          <p className="text-gray-600 dark:text-gray-400">Detailed content performance analytics will be available in future updates.</p>
        </div>
      )}
    </div>
  );
};

export default React.memo(CampaignAnalytics);
