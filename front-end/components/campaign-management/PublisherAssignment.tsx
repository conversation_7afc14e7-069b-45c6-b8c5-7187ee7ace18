'use client';
import React, { useState, useEffect } from 'react';

interface Publisher {
  id: string;
  name: string;
  email: string;
  username?: string;
  followers?: number;
  engagementRate?: number;
  niche?: string;
  isAssigned?: boolean;
  assignedAt?: string;
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
}

interface PublisherAssignmentProps {
  campaign: Campaign;
  onClose: () => void;
  onAssignmentComplete: (updatedCampaign: Campaign) => void;
}

const PublisherAssignment: React.FC<PublisherAssignmentProps> = ({
  campaign,
  onClose,
  onAssignmentComplete
}) => {
  const [publishers, setPublishers] = useState<Publisher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPublishers, setSelectedPublishers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterNiche, setFilterNiche] = useState('all');
  const [sortBy, setSortBy] = useState<'name' | 'followers' | 'engagement'>('name');
  const [assigning, setAssigning] = useState(false);

  // Fetch publishers
  useEffect(() => {
    const fetchPublishers = async () => {
      try {
        setError(null);
        
        const response = await fetch('http://localhost:3000/user?role=publisher', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          const publisherUsers = Array.isArray(data) ? data.map((user: any, index: number) => ({
            id: user.id || `pub-${index}`,
            name: user.name || user.username || user.email,
            email: user.email,
            username: user.username || `publisher${index + 1}`,
            followers: Math.floor(Math.random() * 100000) + 1000, // Mock data
            engagementRate: Math.floor(Math.random() * 10) + 1, // Mock data
            niche: ['lifestyle', 'tech', 'fashion', 'food', 'travel'][Math.floor(Math.random() * 5)], // Mock data
            isAssigned: campaign.assignedPublisherIds.includes(user.id || `pub-${index}`),
            assignedAt: campaign.assignedPublisherIds.includes(user.id || `pub-${index}`) ? new Date().toISOString() : undefined
          })) : [];
          setPublishers(publisherUsers);
        } else {
          throw new Error('Failed to fetch publishers');
        }
      } catch (err) {
        console.error('Failed to fetch publishers:', err);
        setError('Failed to load publishers');
        // Mock data for development
        const mockPublishers: Publisher[] = Array.from({ length: 20 }, (_, index) => ({
          id: `pub-${index + 1}`,
          name: `Publisher ${index + 1}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`,
          followers: Math.floor(Math.random() * 100000) + 1000,
          engagementRate: Math.floor(Math.random() * 10) + 1,
          niche: ['lifestyle', 'tech', 'fashion', 'food', 'travel'][Math.floor(Math.random() * 5)],
          isAssigned: campaign.assignedPublisherIds.includes(`pub-${index + 1}`),
          assignedAt: campaign.assignedPublisherIds.includes(`pub-${index + 1}`) ? new Date().toISOString() : undefined
        }));
        setPublishers(mockPublishers);
      } finally {
        setLoading(false);
      }
    };

    fetchPublishers();
  }, [campaign.assignedPublisherIds]);

  // Filter and sort publishers
  const filteredPublishers = publishers
    .filter(publisher => {
      const matchesSearch = publisher.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           publisher.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (publisher.username && publisher.username.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesNiche = filterNiche === 'all' || publisher.niche === filterNiche;
      return matchesSearch && matchesNiche;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'followers':
          return (b.followers || 0) - (a.followers || 0);
        case 'engagement':
          return (b.engagementRate || 0) - (a.engagementRate || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

  // Handle publisher selection
  const handlePublisherToggle = (publisherId: string) => {
    setSelectedPublishers(prev => 
      prev.includes(publisherId) 
        ? prev.filter(id => id !== publisherId)
        : [...prev, publisherId]
    );
  };

  // Handle select all
  const handleSelectAll = () => {
    const unassignedPublishers = filteredPublishers.filter(p => !p.isAssigned);
    setSelectedPublishers(unassignedPublishers.map(p => p.id));
  };

  // Handle clear selection
  const handleClearSelection = () => {
    setSelectedPublishers([]);
  };

  // Handle assignment
  const handleAssignPublishers = async () => {
    if (selectedPublishers.length === 0) return;

    setAssigning(true);
    try {
      const response = await fetch(`http://localhost:3000/campaign/${campaign.id}/assign-publishers`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          publisherIds: selectedPublishers
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to assign publishers: ${response.statusText}`);
      }

      // Update local state
      setPublishers(prev => prev.map(publisher => 
        selectedPublishers.includes(publisher.id)
          ? { ...publisher, isAssigned: true, assignedAt: new Date().toISOString() }
          : publisher
      ));

      // Update campaign
      const updatedCampaign = {
        ...campaign,
        assignedPublisherIds: [...campaign.assignedPublisherIds, ...selectedPublishers]
      };

      onAssignmentComplete(updatedCampaign);
      setSelectedPublishers([]);
      
      console.log('Publishers assigned successfully');
    } catch (err) {
      console.error('Publisher assignment failed:', err);
      alert('Failed to assign publishers. Please try again.');
    } finally {
      setAssigning(false);
    }
  };

  // Handle unassignment
  const handleUnassignPublisher = async (publisherId: string) => {
    try {
      const response = await fetch(`http://localhost:3000/campaign/${campaign.id}/remove-publishers`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          publisherIds: [publisherId]
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to unassign publisher: ${response.statusText}`);
      }

      // Update local state
      setPublishers(prev => prev.map(publisher => 
        publisher.id === publisherId
          ? { ...publisher, isAssigned: false, assignedAt: undefined }
          : publisher
      ));

      // Update campaign
      const updatedCampaign = {
        ...campaign,
        assignedPublisherIds: campaign.assignedPublisherIds.filter(id => id !== publisherId)
      };

      onAssignmentComplete(updatedCampaign);
      
      console.log('Publisher unassigned successfully');
    } catch (err) {
      console.error('Publisher unassignment failed:', err);
      alert('Failed to unassign publisher. Please try again.');
    }
  };

  // Get unique niches
  const uniqueNiches = Array.from(new Set(publishers.map(p => p.niche).filter(Boolean)));

  const assignedCount = publishers.filter(p => p.isAssigned).length;
  const availableCount = publishers.filter(p => !p.isAssigned).length;

  return (
    <div className="panel">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Assign Publishers
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Campaign: {campaign.title}
          </p>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          disabled={assigning}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{publishers.length}</div>
            <div className="text-sm text-blue-600 dark:text-blue-400">Total Publishers</div>
          </div>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">{assignedCount}</div>
            <div className="text-sm text-green-600 dark:text-green-400">Assigned</div>
          </div>
        </div>
        <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg border border-gray-200 dark:border-gray-800">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">{availableCount}</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Available</div>
          </div>
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">{selectedPublishers.length}</div>
            <div className="text-sm text-purple-600 dark:text-purple-400">Selected</div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Publishers
          </label>
          <input
            type="text"
            id="search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="form-input"
            placeholder="Search by name, email, or username"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="niche" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter by Niche
          </label>
          <select
            id="niche"
            value={filterNiche}
            onChange={(e) => setFilterNiche(e.target.value)}
            className="form-select"
            disabled={loading}
          >
            <option value="all">All Niches</option>
            {uniqueNiches.map(niche => (
              <option key={niche} value={niche}>
                {niche?.charAt(0).toUpperCase() + niche?.slice(1)}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="sort" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Sort by
          </label>
          <select
            id="sort"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'followers' | 'engagement')}
            className="form-select"
            disabled={loading}
          >
            <option value="name">Name</option>
            <option value="followers">Followers</option>
            <option value="engagement">Engagement Rate</option>
          </select>
        </div>

        <div className="flex items-end">
          <div className="flex space-x-2 w-full">
            <button
              onClick={handleSelectAll}
              className="btn btn-outline-secondary btn-sm flex-1"
              disabled={loading || filteredPublishers.filter(p => !p.isAssigned).length === 0}
            >
              Select All Available
            </button>
            <button
              onClick={handleClearSelection}
              className="btn btn-outline-secondary btn-sm flex-1"
              disabled={loading || selectedPublishers.length === 0}
            >
              Clear Selection
            </button>
          </div>
        </div>
      </div>

      {/* Assignment Actions */}
      {selectedPublishers.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
              {selectedPublishers.length} publisher{selectedPublishers.length !== 1 ? 's' : ''} selected
            </div>
            <button
              onClick={handleAssignPublishers}
              className="btn btn-primary btn-sm"
              disabled={assigning}
            >
              {assigning ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Assigning...</span>
                </div>
              ) : (
                `Assign ${selectedPublishers.length} Publisher${selectedPublishers.length !== 1 ? 's' : ''}`
              )}
            </button>
          </div>
        </div>
      )}

      {/* Publishers List */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading publishers...</span>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Publishers</h3>
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
        </div>
      ) : filteredPublishers.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-6xl mb-4">👥</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Publishers Found</h3>
          <p className="text-gray-600 dark:text-gray-400">Try adjusting your search or filter criteria.</p>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredPublishers.map((publisher) => (
            <div
              key={publisher.id}
              className={`p-4 border rounded-lg transition-colors ${
                publisher.isAssigned
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                  : selectedPublishers.includes(publisher.id)
                  ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                  : 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {!publisher.isAssigned && (
                    <input
                      type="checkbox"
                      checked={selectedPublishers.includes(publisher.id)}
                      onChange={() => handlePublisherToggle(publisher.id)}
                      className="form-checkbox h-5 w-5 text-primary"
                      disabled={assigning}
                    />
                  )}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {publisher.name}
                      </h4>
                      {publisher.isAssigned && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                          Assigned
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      @{publisher.username} • {publisher.email}
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <span>👥 {publisher.followers?.toLocaleString()} followers</span>
                      <span>📊 {publisher.engagementRate}% engagement</span>
                      <span>🏷️ {publisher.niche}</span>
                    </div>
                  </div>
                </div>
                {publisher.isAssigned && (
                  <button
                    onClick={() => handleUnassignPublisher(publisher.id)}
                    className="btn btn-outline-danger btn-sm"
                    disabled={assigning}
                  >
                    Unassign
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default React.memo(PublisherAssignment);
