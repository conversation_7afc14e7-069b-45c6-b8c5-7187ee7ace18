'use client';
import React, { useState } from 'react';
import { DataTable } from 'mantine-datatable';
import ApplicationReviewModal from './ApplicationReviewModal';
import BulkReviewModal from './BulkReviewModal';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  reviewer?: {
    id: string;
    email: string;
  };
}

interface ApplicationListProps {
  applications: Application[];
  onReviewApplication: (applicationId: string, action: 'approve' | 'reject', feedback?: string) => Promise<void>;
  onBulkReview?: (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => Promise<void>;
  loading: boolean;
}

const ApplicationList: React.FC<ApplicationListProps> = ({
  applications,
  onReviewApplication,
  onBulkReview,
  loading
}) => {
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [reviewModalOpen, setReviewModalOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedApplicationIds, setSelectedApplicationIds] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState<'approve' | 'reject' | null>(null);
  const [bulkModalOpen, setBulkModalOpen] = useState(false);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Status badge component
  const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Pending' },
      approved: { color: 'bg-green-100 text-green-800 border-green-200', label: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800 border-red-200', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Handle review action
  const handleReview = (application: Application) => {
    setSelectedApplication(application);
    setReviewModalOpen(true);
  };

  // Handle review submission
  const handleReviewSubmit = async (action: 'approve' | 'reject', feedback?: string) => {
    if (selectedApplication) {
      await onReviewApplication(selectedApplication.id, action, feedback);
      setReviewModalOpen(false);
      setSelectedApplication(null);
    }
  };

  // Filter applications based on search and filters
  const filteredApplications = React.useMemo(() => {
    return applications.filter(application => {
      // Search filter
      if (page > 1 && applications.length <= (page - 1) * pageSize) {
        setPage(1); // Reset to first page if current page is out of range
      }

      return true; // All filtering is now handled by the parent component
    });
  }, [applications, page, pageSize]);

  // Handle bulk selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const pendingApplicationIds = filteredApplications
        .filter(app => app.status === 'pending')
        .map(app => app.id);
      setSelectedApplicationIds(pendingApplicationIds);
    } else {
      setSelectedApplicationIds([]);
    }
  };

  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    if (checked) {
      setSelectedApplicationIds(prev => [...prev, applicationId]);
    } else {
      setSelectedApplicationIds(prev => prev.filter(id => id !== applicationId));
    }
  };

  // Handle bulk review
  const handleBulkReview = async (action: 'approve' | 'reject', feedback?: string) => {
    if (onBulkReview && selectedApplicationIds.length > 0) {
      await onBulkReview(selectedApplicationIds, action, feedback);
      setSelectedApplicationIds([]);
      setBulkModalOpen(false);
    }
  };

  // Get pending applications for bulk selection
  const pendingApplications = filteredApplications.filter(app => app.status === 'pending');
  const allPendingSelected = pendingApplications.length > 0 &&
    pendingApplications.every(app => selectedApplicationIds.includes(app.id));

  // Table columns configuration
  const columns = [
    {
      accessor: 'select',
      title: (
        <input
          type="checkbox"
          className="form-checkbox"
          checked={allPendingSelected}
          onChange={(e) => handleSelectAll(e.target.checked)}
          disabled={pendingApplications.length === 0}
        />
      ),
      render: (application: Application) => (
        <input
          type="checkbox"
          className="form-checkbox"
          checked={selectedApplicationIds.includes(application.id)}
          onChange={(e) => handleSelectApplication(application.id, e.target.checked)}
          disabled={application.status !== 'pending'}
        />
      ),
    },
    {
      accessor: 'publisher.email',
      title: 'Publisher',
      sortable: true,
      render: (application: Application) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {application.publisher.username || application.publisher.email}
          </span>
          {application.publisher.username && (
            <span className="text-sm text-gray-500">{application.publisher.email}</span>
          )}
        </div>
      ),
    },
    {
      accessor: 'campaign.title',
      title: 'Campaign',
      sortable: true,
      render: (application: Application) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {application.campaign.title}
          </span>
          <span className="text-sm text-gray-500">
            by {application.campaign.advertiser.email}
          </span>
        </div>
      ),
    },
    {
      accessor: 'status',
      title: 'Status',
      sortable: true,
      render: (application: Application) => <StatusBadge status={application.status} />,
    },
    {
      accessor: 'createdAt',
      title: 'Applied Date',
      sortable: true,
      render: (application: Application) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {formatDate(application.createdAt)}
        </span>
      ),
    },
    {
      accessor: 'reviewer',
      title: 'Reviewer',
      render: (application: Application) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {application.reviewer ? application.reviewer.email : '-'}
        </span>
      ),
    },
    {
      accessor: 'actions',
      title: 'Actions',
      textAlignment: 'center' as const,
      render: (application: Application) => (
        <div className="flex items-center justify-center space-x-2">
          {application.status === 'pending' ? (
            <button
              onClick={() => handleReview(application)}
              className="btn btn-sm btn-primary"
            >
              Review
            </button>
          ) : (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleReview(application)}
                className="btn btn-sm btn-outline-primary"
              >
                View Details
              </button>
              <button
                onClick={() => handleReview(application)}
                className="btn btn-sm btn-outline-secondary"
              >
                Re-review
              </button>
            </div>
          )}
        </div>
      ),
    },
  ];

  // Pagination
  const paginatedApplications = filteredApplications.slice((page - 1) * pageSize, page * pageSize);

  return (
    <div className="space-y-4">
      {/* Table Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Applications ({filteredApplications.length})
          </h2>
          {selectedApplicationIds.length > 0 && (
            <div className="flex flex-col sm:flex-row sm:items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {selectedApplicationIds.length} selected
              </span>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => {
                    setBulkAction('approve');
                    setBulkModalOpen(true);
                  }}
                  className="btn btn-sm btn-success"
                  disabled={selectedApplicationIds.length === 0}
                  aria-label={`Bulk approve ${selectedApplicationIds.length} selected applications`}
                >
                  Bulk Approve
                </button>
                <button
                  onClick={() => {
                    setBulkAction('reject');
                    setBulkModalOpen(true);
                  }}
                  className="btn btn-sm btn-danger"
                  disabled={selectedApplicationIds.length === 0}
                  aria-label={`Bulk reject ${selectedApplicationIds.length} selected applications`}
                >
                  Bulk Reject
                </button>
                <button
                  onClick={() => setSelectedApplicationIds([])}
                  className="btn btn-sm btn-outline-secondary"
                  aria-label="Clear selection"
                >
                  Clear Selection
                </button>
              </div>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <label htmlFor="page-size" className="text-sm text-gray-600 dark:text-gray-400">Show:</label>
          <select
            id="page-size"
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value));
              setPage(1);
            }}
            className="form-select form-select-sm w-20"
            aria-label="Number of applications per page"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
      </div>

      {/* Data Table */}
      <div className="datatables">
        <DataTable
          className="whitespace-nowrap table-hover"
          records={paginatedApplications}
          columns={columns}
          totalRecords={filteredApplications.length}
          recordsPerPage={pageSize}
          page={page}
          onPageChange={setPage}
          recordsPerPageOptions={[10, 25, 50, 100]}
          onRecordsPerPageChange={setPageSize}
          minHeight={200}
          noRecordsText="No applications found"
          loadingText="Loading applications..."
          fetching={loading}
          paginationText={({ from, to, totalRecords }) =>
            `Showing ${from} to ${to} of ${totalRecords} applications`
          }
        />
      </div>

      {/* Review Modal */}
      {selectedApplication && (
        <ApplicationReviewModal
          application={selectedApplication}
          isOpen={reviewModalOpen}
          onClose={() => {
            setReviewModalOpen(false);
            setSelectedApplication(null);
          }}
          onSubmit={handleReviewSubmit}
        />
      )}

      {/* Bulk Review Modal */}
      {bulkModalOpen && bulkAction && (
        <BulkReviewModal
          applicationIds={selectedApplicationIds}
          applications={filteredApplications.filter(app => selectedApplicationIds.includes(app.id))}
          action={bulkAction}
          isOpen={bulkModalOpen}
          onClose={() => {
            setBulkModalOpen(false);
            setBulkAction(null);
          }}
          onSubmit={handleBulkReview}
        />
      )}
    </div>
  );
};

export default React.memo(ApplicationList);
