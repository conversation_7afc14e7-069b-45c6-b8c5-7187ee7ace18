'use client';
import React, { useState } from 'react';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  reviewer?: {
    id: string;
    email: string;
  };
}

interface BulkReviewModalProps {
  applicationIds: string[];
  applications: Application[];
  action: 'approve' | 'reject';
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (action: 'approve' | 'reject', feedback?: string) => Promise<void>;
}

const BulkReviewModal: React.FC<BulkReviewModalProps> = ({
  applicationIds,
  applications,
  action,
  isOpen,
  onClose,
  onSubmit
}) => {
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async () => {
    if (action === 'reject' && !feedback.trim()) {
      alert('Please provide feedback for bulk rejection');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(action, feedback.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to submit bulk review:', error);
      alert('Failed to submit bulk review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const actionColor = action === 'approve' ? 'green' : 'red';
  const actionText = action === 'approve' ? 'Approve' : 'Reject';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Bulk {actionText} Applications
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={isSubmitting}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Warning/Confirmation */}
          <div className={`p-4 rounded-lg border ${
            action === 'approve' 
              ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' 
              : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
          }`}>
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg 
                  className={`w-5 h-5 ${action === 'approve' ? 'text-green-400' : 'text-red-400'}`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d={action === 'approve' 
                      ? "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                      : "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    } 
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h4 className={`text-sm font-medium ${
                  action === 'approve' 
                    ? 'text-green-800 dark:text-green-200' 
                    : 'text-red-800 dark:text-red-200'
                }`}>
                  {action === 'approve' ? 'Bulk Approval Confirmation' : 'Bulk Rejection Warning'}
                </h4>
                <p className={`mt-1 text-sm ${
                  action === 'approve' 
                    ? 'text-green-700 dark:text-green-300' 
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  You are about to {action} {applicationIds.length} application{applicationIds.length > 1 ? 's' : ''}. 
                  This action cannot be undone.
                </p>
              </div>
            </div>
          </div>

          {/* Applications List */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Selected Applications ({applicationIds.length})
            </h4>
            <div className="max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {applications.map((application) => (
                  <div key={application.id} className="p-3 flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {application.publisher.username || application.publisher.email}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Campaign: {application.campaign.title}
                      </p>
                    </div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      {application.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Feedback Input */}
          <div>
            <label htmlFor="bulk-feedback" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {action === 'approve' ? 'Approval Message (Optional)' : 'Rejection Reason (Required)'}
              {action === 'reject' && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              id="bulk-feedback"
              rows={4}
              className="form-textarea w-full"
              placeholder={action === 'approve' 
                ? "Optional: Add a message for all approved applications..." 
                : "Required: Provide a reason for rejecting these applications..."
              }
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              disabled={isSubmitting}
            />
            {action === 'reject' && (
              <p className="text-xs text-red-600 mt-1">
                A rejection reason is required for bulk rejection
              </p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="btn btn-outline-secondary"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className={`btn ${action === 'approve' ? 'btn-success' : 'btn-danger'}`}
            disabled={isSubmitting || (action === 'reject' && !feedback.trim())}
          >
            {isSubmitting 
              ? `${actionText}ing ${applicationIds.length} Application${applicationIds.length > 1 ? 's' : ''}...` 
              : `${actionText} ${applicationIds.length} Application${applicationIds.length > 1 ? 's' : ''}`
            }
          </button>
        </div>
      </div>
    </div>
  );
};

export default BulkReviewModal;
