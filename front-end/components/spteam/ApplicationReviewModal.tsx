'use client';
import React, { useState } from 'react';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  reviewer?: {
    id: string;
    email: string;
  };
}

interface ApplicationReviewModalProps {
  application: Application;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (action: 'approve' | 'reject', feedback?: string) => Promise<void>;
}

const ApplicationReviewModal: React.FC<ApplicationReviewModalProps> = ({
  application,
  isOpen,
  onClose,
  onSubmit
}) => {
  const [feedback, setFeedback] = useState(application.feedback || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'reject' | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  if (!isOpen) return null;

  const handleActionClick = (action: 'approve' | 'reject') => {
    if (action === 'reject' && !feedback.trim()) {
      alert('Please provide feedback for rejection');
      return;
    }
    setSelectedAction(action);
    setShowConfirmation(true);
  };

  const handleConfirmSubmit = async () => {
    if (!selectedAction) return;

    setIsSubmitting(true);
    try {
      await onSubmit(selectedAction, feedback.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to submit review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
      setShowConfirmation(false);
      setSelectedAction(null);
    }
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    setSelectedAction(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Review Application
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={isSubmitting}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Application Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Publisher Information</h4>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {application.publisher.username || 'No username'}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {application.publisher.email}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    ID: {application.publisher.id}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Application Status</h4>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      application.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      application.status === 'approved' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                    Applied: {formatDate(application.createdAt)}
                  </p>
                  {application.updatedAt !== application.createdAt && (
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      Updated: {formatDate(application.updatedAt)}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Campaign Information</h4>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {application.campaign.title}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Advertiser: {application.campaign.advertiser.email}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    Campaign ID: {application.campaign.id}
                  </p>
                </div>
              </div>

              {application.reviewer && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Reviewer Information</h4>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {application.reviewer.email}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Current Feedback */}
          {application.feedback && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Feedback</h4>
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {application.feedback}
                </p>
              </div>
            </div>
          )}

          {/* Feedback Input */}
          <div>
            <label htmlFor="feedback" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Review Feedback {selectedAction === 'reject' && <span className="text-red-500">*</span>}
            </label>
            <textarea
              id="feedback"
              rows={4}
              className="form-textarea w-full"
              placeholder={selectedAction === 'approve' ?
                "Optional: Add any comments about the approval..." :
                "Required: Please provide detailed feedback for rejection..."
              }
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              disabled={isSubmitting}
            />
            {selectedAction === 'reject' && (
              <p className="text-xs text-red-600 mt-1">
                Feedback is required when rejecting an application
              </p>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          {!showConfirmation ? (
            <>
              <button
                onClick={onClose}
                className="btn btn-outline-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={() => handleActionClick('reject')}
                className="btn btn-danger"
                disabled={isSubmitting}
              >
                Reject
              </button>
              <button
                onClick={() => handleActionClick('approve')}
                className="btn btn-success"
                disabled={isSubmitting}
              >
                Approve
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleCancelConfirmation}
                className="btn btn-outline-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSubmit}
                className={`btn ${selectedAction === 'approve' ? 'btn-success' : 'btn-danger'}`}
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? `${selectedAction === 'approve' ? 'Approving' : 'Rejecting'}...`
                  : `Confirm ${selectedAction === 'approve' ? 'Approval' : 'Rejection'}`
                }
              </button>
            </>
          )}
        </div>

        {/* Confirmation Message */}
        {showConfirmation && (
          <div className="px-6 pb-4">
            <div className={`p-3 rounded-lg ${
              selectedAction === 'approve'
                ? 'bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800'
                : 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'
            }`}>
              <p className={`text-sm ${
                selectedAction === 'approve'
                  ? 'text-green-800 dark:text-green-200'
                  : 'text-red-800 dark:text-red-200'
              }`}>
                Are you sure you want to {selectedAction} this application? This action cannot be undone.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationReviewModal;
