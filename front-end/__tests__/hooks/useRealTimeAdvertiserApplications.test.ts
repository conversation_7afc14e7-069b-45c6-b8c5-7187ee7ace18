import { renderHook, act, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import useRealTimeAdvertiserApplications from '@/hooks/useRealTimeAdvertiserApplications';

// Mock the auth slice
const mockAuthSlice = {
  name: 'auth',
  initialState: {
    user: {
      id: 'adv-1',
      email: '<EMAIL>',
      role: 'advertiser'
    }
  },
  reducers: {},
  selectors: {
    selectUser: (state: any) => state.auth.user
  }
};

// Create a mock store
const createMockStore = (user: any = null) => {
  return configureStore({
    reducer: {
      auth: (state = { user }, action) => state
    }
  });
};

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(() => 'mock-token'),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// Mock console methods
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

describe('useRealTimeAdvertiserApplications', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
    mockFetch.mockClear();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const renderHookWithProvider = (user: any = null, pollingInterval?: number) => {
    const store = createMockStore(user);
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <Provider store={store}>{children}</Provider>
    );
    
    return renderHook(() => useRealTimeAdvertiserApplications(pollingInterval), { wrapper });
  };

  it('initializes with loading state', () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };
    const { result } = renderHookWithProvider(user);

    expect(result.current.loading).toBe(true);
    expect(result.current.applications).toEqual([]);
    expect(result.current.error).toBe(null);
    expect(result.current.lastUpdated).toBe(null);
  });

  it('does not fetch when user is not advertiser', () => {
    const user = { id: 'pub-1', email: '<EMAIL>', role: 'publisher' };
    const { result } = renderHookWithProvider(user);

    expect(result.current.loading).toBe(false);
    expect(mockFetch).not.toHaveBeenCalled();
  });

  it('fetches applications successfully', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };
    const mockApplications = [
      { id: 'app-1', status: 'approved', createdAt: '2024-01-01T00:00:00Z' },
      { id: 'app-2', status: 'pending', createdAt: '2024-01-02T00:00:00Z' }
    ];

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApplications
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.applications).toHaveLength(2);
    expect(result.current.error).toBe(null);
    expect(result.current.lastUpdated).toBeInstanceOf(Date);
  });

  it('handles fetch errors', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.applications).toEqual([]);
    expect(result.current.error).toBe('Network error');
    expect(result.current.lastUpdated).toBe(null);
  });

  it('handles 401 unauthorized error', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      statusText: 'Unauthorized'
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Unauthorized. Please log in again.');
  });

  it('handles 403 forbidden error', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 403,
      statusText: 'Forbidden'
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Access denied. Advertiser role required.');
  });

  it('transforms application data correctly', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };
    const mockApplications = [
      { 
        id: 'app-1', 
        status: 'approved', 
        createdAt: '2024-01-01T00:00:00Z',
        feedback: 'Good application'
      }
    ];

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockApplications
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const application = result.current.applications[0];
    expect(application.id).toBe('app-1');
    expect(application.status).toBe('sp_approved'); // Mapped from 'approved'
    expect(application.campaign.advertiser.id).toBe('adv-1');
    expect(application.campaign.advertiser.email).toBe('<EMAIL>');
    expect(application.publisher.email).toBe('<EMAIL>');
    expect(application.feedback).toBe('Good application');
  });

  it('sets up polling with correct interval', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };
    const pollingInterval = 5000; // 5 seconds

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => []
    });

    renderHookWithProvider(user, pollingInterval);

    // Initial fetch
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // Fast-forward time to trigger polling
    act(() => {
      jest.advanceTimersByTime(pollingInterval);
    });

    expect(mockFetch).toHaveBeenCalledTimes(2);

    // Fast-forward again
    act(() => {
      jest.advanceTimersByTime(pollingInterval);
    });

    expect(mockFetch).toHaveBeenCalledTimes(3);
  });

  it('refetch function works correctly', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => []
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    mockFetch.mockClear();

    // Call refetch
    act(() => {
      result.current.refetch();
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
  });

  it('makes API call with correct headers', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => []
    });

    renderHookWithProvider(user);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/applications',
        {
          headers: {
            'Authorization': 'Bearer mock-token',
            'Content-Type': 'application/json',
          },
        }
      );
    });
  });

  it('handles non-array response gracefully', async () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => null // Non-array response
    });

    const { result } = renderHookWithProvider(user);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.applications).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it('cleans up polling on unmount', () => {
    const user = { id: 'adv-1', email: '<EMAIL>', role: 'advertiser' };

    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => []
    });

    const { unmount } = renderHookWithProvider(user);

    // Verify polling is set up
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // Unmount the hook
    unmount();

    // Fast-forward time - polling should not continue
    act(() => {
      jest.advanceTimersByTime(30000);
    });

    // Should still be only the initial call
    expect(mockFetch).toHaveBeenCalledTimes(1);
  });
});
