import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationFilters from '../../../components/spteam/ApplicationFilters';

// Mock application data for testing
const mockApplications = [
  {
    id: '1',
    campaign: {
      id: 'camp1',
      title: 'Test Campaign 1',
      advertiser: {
        id: 'adv1',
        email: '<EMAIL>'
      }
    },
    publisher: {
      id: 'pub1',
      email: '<EMAIL>',
      username: 'publisher1'
    },
    status: 'pending' as const,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    campaign: {
      id: 'camp2',
      title: 'Test Campaign 2',
      advertiser: {
        id: 'adv2',
        email: '<EMAIL>'
      }
    },
    publisher: {
      id: 'pub2',
      email: '<EMAIL>'
    },
    status: 'approved' as const,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    campaign: {
      id: 'camp1',
      title: 'Test Campaign 1',
      advertiser: {
        id: 'adv1',
        email: '<EMAIL>'
      }
    },
    publisher: {
      id: 'pub3',
      email: '<EMAIL>'
    },
    status: 'rejected' as const,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z'
  }
];

const mockFilters = {
  status: 'all',
  search: '',
  campaignId: 'all'
};

const mockOnFilterChange = jest.fn();

describe('ApplicationFilters', () => {
  beforeEach(() => {
    mockOnFilterChange.mockClear();
  });

  it('renders all filter components', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    // Check if search input is rendered
    expect(screen.getByPlaceholderText(/search by publisher email/i)).toBeInTheDocument();

    // Check if status filter is rendered
    expect(screen.getByLabelText(/status/i)).toBeInTheDocument();

    // Check if campaign filter is rendered
    expect(screen.getByLabelText(/campaign/i)).toBeInTheDocument();
  });

  it('displays correct status counts', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    // Check status summary cards by finding the specific sections
    expect(screen.getByText('Total')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Approved')).toBeInTheDocument();
    expect(screen.getByText('Rejected')).toBeInTheDocument();

    // Check that the total count is 3
    expect(screen.getByText('3')).toBeInTheDocument();

    // Check that there are multiple "1" counts (for pending, approved, rejected)
    expect(screen.getAllByText('1')).toHaveLength(3);
  });

  it('calls onFilterChange when search input changes', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    const searchInput = screen.getByPlaceholderText(/search by publisher email/i);
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ search: 'test search' });
  });

  it('calls onFilterChange when status filter changes', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    const statusSelect = screen.getByLabelText(/status/i);
    fireEvent.change(statusSelect, { target: { value: 'pending' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ status: 'pending' });
  });

  it('calls onFilterChange when campaign filter changes', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    const campaignSelect = screen.getByLabelText(/campaign/i);
    fireEvent.change(campaignSelect, { target: { value: 'camp1' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ campaignId: 'camp1' });
  });

  it('shows clear filters button when filters are active', () => {
    const activeFilters = {
      status: 'pending',
      search: 'test',
      campaignId: 'camp1'
    };

    render(
      <ApplicationFilters
        filters={activeFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    const clearButton = screen.getByText(/clear all filters/i);
    expect(clearButton).toBeInTheDocument();

    fireEvent.click(clearButton);
    expect(mockOnFilterChange).toHaveBeenCalledWith({
      search: '',
      status: 'all',
      campaignId: 'all'
    });
  });

  it('displays unique campaigns in campaign filter', () => {
    render(
      <ApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
      />
    );

    const campaignSelect = screen.getByLabelText(/campaign/i);

    // Should have "All Campaigns" option plus unique campaigns
    expect(campaignSelect).toHaveTextContent('All Campaigns');
    expect(campaignSelect).toHaveTextContent('Test Campaign 1');
    expect(campaignSelect).toHaveTextContent('Test Campaign 2');
  });
});
