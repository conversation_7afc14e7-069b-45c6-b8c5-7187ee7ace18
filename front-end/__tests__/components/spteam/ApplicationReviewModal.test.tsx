import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationReviewModal from '@/components/spteam/ApplicationReviewModal';

// Mock application data
const mockApplication = {
  id: 'app-1',
  campaign: {
    id: 'camp-1',
    title: 'Test Campaign',
    advertiser: {
      id: 'adv-1',
      email: '<EMAIL>'
    }
  },
  publisher: {
    id: 'pub-1',
    email: '<EMAIL>',
    username: 'testpublisher'
  },
  status: 'pending' as const,
  feedback: 'Initial feedback',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  reviewer: {
    id: 'rev-1',
    email: '<EMAIL>'
  }
};

const mockProps = {
  application: mockApplication,
  isOpen: true,
  onClose: jest.fn(),
  onSubmit: jest.fn()
};

describe('ApplicationReviewModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    expect(screen.getByText('Review Application')).toBeInTheDocument();
    expect(screen.getByText('testpublisher')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<ApplicationReviewModal {...mockProps} isOpen={false} />);
    
    expect(screen.queryByText('Review Application')).not.toBeInTheDocument();
  });

  it('displays application details correctly', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    // Publisher information
    expect(screen.getByText('testpublisher')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    
    // Campaign information
    expect(screen.getByText('Test Campaign')).toBeInTheDocument();
    expect(screen.getByText('Advertiser: <EMAIL>')).toBeInTheDocument();
    
    // Status
    expect(screen.getByText('Pending')).toBeInTheDocument();
    
    // Reviewer information
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    
    // Current feedback
    expect(screen.getByText('Initial feedback')).toBeInTheDocument();
  });

  it('shows formatted dates correctly', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    expect(screen.getByText(/Applied:/)).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when X button is clicked', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('shows confirmation dialog when approve is clicked', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Approve'));
    
    expect(screen.getByText('Are you sure you want to approve this application? This action cannot be undone.')).toBeInTheDocument();
    expect(screen.getByText('Confirm Approval')).toBeInTheDocument();
  });

  it('shows confirmation dialog when reject is clicked with feedback', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Not suitable' } });
    
    fireEvent.click(screen.getByText('Reject'));
    
    expect(screen.getByText('Are you sure you want to reject this application? This action cannot be undone.')).toBeInTheDocument();
    expect(screen.getByText('Confirm Rejection')).toBeInTheDocument();
  });

  it('prevents rejection without feedback', () => {
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation();
    render(<ApplicationReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Reject'));
    
    expect(alertSpy).toHaveBeenCalledWith('Please provide feedback for rejection');
    expect(screen.queryByText('Confirm Rejection')).not.toBeInTheDocument();
    
    alertSpy.mockRestore();
  });

  it('allows canceling confirmation', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Approve'));
    expect(screen.getByText('Confirm Approval')).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(screen.queryByText('Confirm Approval')).not.toBeInTheDocument();
  });

  it('calls onSubmit when approval is confirmed', async () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Great application!' } });
    
    fireEvent.click(screen.getByText('Approve'));
    fireEvent.click(screen.getByText('Confirm Approval'));
    
    await waitFor(() => {
      expect(mockProps.onSubmit).toHaveBeenCalledWith('approve', 'Great application!');
    });
  });

  it('calls onSubmit when rejection is confirmed', async () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Not suitable' } });
    
    fireEvent.click(screen.getByText('Reject'));
    fireEvent.click(screen.getByText('Confirm Rejection'));
    
    await waitFor(() => {
      expect(mockProps.onSubmit).toHaveBeenCalledWith('reject', 'Not suitable');
    });
  });

  it('shows loading state during submission', async () => {
    const mockOnSubmit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<ApplicationReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByText('Approve'));
    fireEvent.click(screen.getByText('Confirm Approval'));
    
    expect(screen.getByText('Approving...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText('Approving...')).not.toBeInTheDocument();
    });
  });

  it('disables buttons during submission', async () => {
    const mockOnSubmit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<ApplicationReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByText('Approve'));
    const confirmButton = screen.getByText('Confirm Approval');
    const cancelButton = screen.getByText('Cancel');
    
    fireEvent.click(confirmButton);
    
    expect(confirmButton).toBeDisabled();
    expect(cancelButton).toBeDisabled();
    
    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });

  it('handles submission errors gracefully', async () => {
    const mockOnSubmit = jest.fn(() => Promise.reject(new Error('Network error')));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation();
    
    render(<ApplicationReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByText('Approve'));
    fireEvent.click(screen.getByText('Confirm Approval'));
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to submit review:', expect.any(Error));
      expect(alertSpy).toHaveBeenCalledWith('Failed to submit review. Please try again.');
    });
    
    consoleSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('updates feedback text correctly', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Updated feedback' } });
    
    expect(textarea).toHaveValue('Updated feedback');
  });

  it('shows required indicator for rejection feedback', () => {
    render(<ApplicationReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Reject'));
    
    expect(screen.getByText('Required: Please provide detailed feedback for rejection...')).toBeInTheDocument();
  });

  it('handles application without reviewer', () => {
    const appWithoutReviewer = { ...mockApplication, reviewer: undefined };
    render(<ApplicationReviewModal {...mockProps} application={appWithoutReviewer} />);
    
    expect(screen.queryByText('Reviewer Information')).not.toBeInTheDocument();
  });

  it('handles application without username', () => {
    const appWithoutUsername = { 
      ...mockApplication, 
      publisher: { ...mockApplication.publisher, username: undefined }
    };
    render(<ApplicationReviewModal {...mockProps} application={appWithoutUsername} />);
    
    expect(screen.getByText('No username')).toBeInTheDocument();
  });
});
