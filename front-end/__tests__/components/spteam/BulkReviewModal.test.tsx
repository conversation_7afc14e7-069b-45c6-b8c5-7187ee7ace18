import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import BulkReviewModal from '@/components/spteam/BulkReviewModal';

// Mock applications data
const mockApplications = [
  {
    id: 'app-1',
    campaign: {
      id: 'camp-1',
      title: 'Test Campaign 1',
      advertiser: {
        id: 'adv-1',
        email: '<EMAIL>'
      }
    },
    publisher: {
      id: 'pub-1',
      email: '<EMAIL>',
      username: 'publisher1'
    },
    status: 'pending' as const,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'app-2',
    campaign: {
      id: 'camp-2',
      title: 'Test Campaign 2',
      advertiser: {
        id: 'adv-2',
        email: '<EMAIL>'
      }
    },
    publisher: {
      id: 'pub-2',
      email: '<EMAIL>',
      username: 'publisher2'
    },
    status: 'pending' as const,
    createdAt: '2023-01-02T00:00:00Z',
    updatedAt: '2023-01-02T00:00:00Z'
  }
];

const mockProps = {
  applicationIds: ['app-1', 'app-2'],
  applications: mockApplications,
  action: 'approve' as const,
  isOpen: true,
  onClose: jest.fn(),
  onSubmit: jest.fn()
};

describe('BulkReviewModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<BulkReviewModal {...mockProps} />);
    
    expect(screen.getByText('Bulk Approve Applications')).toBeInTheDocument();
    expect(screen.getByText('Selected Applications (2)')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<BulkReviewModal {...mockProps} isOpen={false} />);
    
    expect(screen.queryByText('Bulk Approve Applications')).not.toBeInTheDocument();
  });

  it('displays correct action text for approve', () => {
    render(<BulkReviewModal {...mockProps} action="approve" />);
    
    expect(screen.getByText('Bulk Approve Applications')).toBeInTheDocument();
    expect(screen.getByText('Bulk Approval Confirmation')).toBeInTheDocument();
    expect(screen.getByText('Approve 2 Applications')).toBeInTheDocument();
  });

  it('displays correct action text for reject', () => {
    render(<BulkReviewModal {...mockProps} action="reject" />);
    
    expect(screen.getByText('Bulk Reject Applications')).toBeInTheDocument();
    expect(screen.getByText('Bulk Rejection Warning')).toBeInTheDocument();
    expect(screen.getByText('Reject 2 Applications')).toBeInTheDocument();
  });

  it('shows all selected applications', () => {
    render(<BulkReviewModal {...mockProps} />);
    
    expect(screen.getByText('publisher1')).toBeInTheDocument();
    expect(screen.getByText('Campaign: Test Campaign 1')).toBeInTheDocument();
    expect(screen.getByText('publisher2')).toBeInTheDocument();
    expect(screen.getByText('Campaign: Test Campaign 2')).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<BulkReviewModal {...mockProps} />);
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when X button is clicked', () => {
    render(<BulkReviewModal {...mockProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    expect(mockProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('allows submission for approve without feedback', async () => {
    render(<BulkReviewModal {...mockProps} action="approve" />);
    
    const submitButton = screen.getByText('Approve 2 Applications');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onSubmit).toHaveBeenCalledWith('approve', undefined);
    });
  });

  it('allows submission for approve with feedback', async () => {
    render(<BulkReviewModal {...mockProps} action="approve" />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Great applications!' } });
    
    const submitButton = screen.getByText('Approve 2 Applications');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onSubmit).toHaveBeenCalledWith('approve', 'Great applications!');
    });
  });

  it('requires feedback for rejection', () => {
    render(<BulkReviewModal {...mockProps} action="reject" />);
    
    const submitButton = screen.getByText('Reject 2 Applications');
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when feedback is provided for rejection', () => {
    render(<BulkReviewModal {...mockProps} action="reject" />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Not suitable' } });
    
    const submitButton = screen.getByText('Reject 2 Applications');
    expect(submitButton).not.toBeDisabled();
  });

  it('calls onSubmit with feedback for rejection', async () => {
    render(<BulkReviewModal {...mockProps} action="reject" />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: 'Not suitable' } });
    
    const submitButton = screen.getByText('Reject 2 Applications');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onSubmit).toHaveBeenCalledWith('reject', 'Not suitable');
    });
  });

  it('shows loading state during submission', async () => {
    const mockOnSubmit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<BulkReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByText('Approve 2 Applications');
    fireEvent.click(submitButton);
    
    expect(screen.getByText('Approving 2 Applications...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText('Approving 2 Applications...')).not.toBeInTheDocument();
    });
  });

  it('disables buttons during submission', async () => {
    const mockOnSubmit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
    render(<BulkReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByText('Approve 2 Applications');
    const cancelButton = screen.getByText('Cancel');
    
    fireEvent.click(submitButton);
    
    expect(submitButton).toBeDisabled();
    expect(cancelButton).toBeDisabled();
    
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
      expect(cancelButton).not.toBeDisabled();
    });
  });

  it('handles submission errors gracefully', async () => {
    const mockOnSubmit = jest.fn(() => Promise.reject(new Error('Network error')));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation();
    
    render(<BulkReviewModal {...mockProps} onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByText('Approve 2 Applications');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to submit bulk review:', expect.any(Error));
      expect(alertSpy).toHaveBeenCalledWith('Failed to submit bulk review. Please try again.');
    });
    
    consoleSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('has proper accessibility attributes', () => {
    render(<BulkReviewModal {...mockProps} />);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveAttribute('aria-label');
    
    const modal = screen.getByRole('dialog', { hidden: true });
    expect(modal).toBeInTheDocument();
  });
});
