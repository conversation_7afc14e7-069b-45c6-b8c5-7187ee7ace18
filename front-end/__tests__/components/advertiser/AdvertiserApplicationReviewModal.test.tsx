import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdvertiserApplicationReviewModal from '@/components/advertiser/AdvertiserApplicationReviewModal';

// Mock application data
const mockApplication = {
  id: 'app-1',
  campaign: {
    id: 'camp-1',
    title: 'Summer Campaign',
    advertiser: { id: 'adv-1', email: '<EMAIL>' }
  },
  publisher: {
    id: 'pub-1',
    email: '<EMAIL>',
    username: 'publisher1'
  },
  status: 'sp_approved' as const,
  feedback: 'Initial feedback',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  spReviewer: {
    id: 'sp-1',
    email: '<EMAIL>'
  }
};

const mockOnClose = jest.fn();
const mockOnSubmit = jest.fn();

describe('AdvertiserApplicationReviewModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('does not render when isOpen is false', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={false}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.queryByText('Application Review - Final Approval')).not.toBeInTheDocument();
  });

  it('renders modal content when isOpen is true', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Application Review - Final Approval')).toBeInTheDocument();
    expect(screen.getByText('Final Review Required')).toBeInTheDocument();
    expect(screen.getByText('publisher1')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Summer Campaign')).toBeInTheDocument();
  });

  it('displays SP Team review status when available', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Approved by SP Team')).toBeInTheDocument();
    expect(screen.getByText('Reviewed by: <EMAIL>')).toBeInTheDocument();
  });

  it('displays previous feedback when available', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Previous Feedback')).toBeInTheDocument();
    expect(screen.getByText('Initial feedback')).toBeInTheDocument();
  });

  it('shows approve and reject buttons for sp_approved applications', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Approve Application')).toBeInTheDocument();
    expect(screen.getByText('Reject Application')).toBeInTheDocument();
  });

  it('does not show approve/reject buttons for non-sp_approved applications', () => {
    const approvedApplication = { ...mockApplication, status: 'approved' as const };
    
    render(
      <AdvertiserApplicationReviewModal
        application={approvedApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.queryByText('Approve Application')).not.toBeInTheDocument();
    expect(screen.queryByText('Reject Application')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    const closeButton = screen.getByLabelText('Close modal');
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when cancel button is clicked', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('shows confirmation dialog when approve is clicked', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    const approveButton = screen.getByText('Approve Application');
    fireEvent.click(approveButton);

    expect(screen.getByText('Confirm Approval')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to approve this application/)).toBeInTheDocument();
  });

  it('shows confirmation dialog when reject is clicked with feedback', () => {
    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    // Add feedback first
    const feedbackTextarea = screen.getByLabelText(/Your Decision Feedback/);
    fireEvent.change(feedbackTextarea, { target: { value: 'Rejection reason' } });

    const rejectButton = screen.getByText('Reject Application');
    fireEvent.click(rejectButton);

    expect(screen.getByText('Confirm Rejection')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to reject this application/)).toBeInTheDocument();
  });

  it('shows alert when trying to reject without feedback', () => {
    // Mock window.alert
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    const rejectButton = screen.getByText('Reject Application');
    fireEvent.click(rejectButton);

    expect(alertSpy).toHaveBeenCalledWith('Please provide feedback for rejection');
    
    alertSpy.mockRestore();
  });

  it('calls onSubmit with correct parameters when approval is confirmed', async () => {
    mockOnSubmit.mockResolvedValue(undefined);

    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    // Add feedback
    const feedbackTextarea = screen.getByLabelText(/Your Decision Feedback/);
    fireEvent.change(feedbackTextarea, { target: { value: 'Approval feedback' } });

    // Click approve
    const approveButton = screen.getByText('Approve Application');
    fireEvent.click(approveButton);

    // Confirm approval
    const confirmButton = screen.getByText('Confirm Approval');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith('approve', 'Approval feedback');
    });
  });

  it('calls onSubmit with correct parameters when rejection is confirmed', async () => {
    mockOnSubmit.mockResolvedValue(undefined);

    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    // Add feedback
    const feedbackTextarea = screen.getByLabelText(/Your Decision Feedback/);
    fireEvent.change(feedbackTextarea, { target: { value: 'Rejection reason' } });

    // Click reject
    const rejectButton = screen.getByText('Reject Application');
    fireEvent.click(rejectButton);

    // Confirm rejection
    const confirmButton = screen.getByText('Confirm Rejection');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith('reject', 'Rejection reason');
    });
  });

  it('shows loading state during submission', async () => {
    // Mock a delayed submission
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    // Add feedback and approve
    const feedbackTextarea = screen.getByLabelText(/Your Decision Feedback/);
    fireEvent.change(feedbackTextarea, { target: { value: 'Approval feedback' } });

    const approveButton = screen.getByText('Approve Application');
    fireEvent.click(approveButton);

    const confirmButton = screen.getByText('Confirm Approval');
    fireEvent.click(confirmButton);

    // Check loading state
    expect(screen.getByText('Approving...')).toBeInTheDocument();

    // Wait for completion
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalled();
    });
  });

  it('handles submission errors gracefully', async () => {
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));

    render(
      <AdvertiserApplicationReviewModal
        application={mockApplication}
        isOpen={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    );

    // Add feedback and approve
    const feedbackTextarea = screen.getByLabelText(/Your Decision Feedback/);
    fireEvent.change(feedbackTextarea, { target: { value: 'Approval feedback' } });

    const approveButton = screen.getByText('Approve Application');
    fireEvent.click(approveButton);

    const confirmButton = screen.getByText('Confirm Approval');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith('Failed to submit review. Please try again.');
    });

    alertSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });
});
