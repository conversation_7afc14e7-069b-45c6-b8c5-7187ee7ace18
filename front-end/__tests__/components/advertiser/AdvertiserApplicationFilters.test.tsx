import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdvertiserApplicationFilters from '@/components/advertiser/AdvertiserApplicationFilters';

// Mock application data
const mockApplications = [
  {
    id: 'app-1',
    campaign: {
      id: 'camp-1',
      title: 'Summer Campaign',
      advertiser: { id: 'adv-1', email: '<EMAIL>' }
    },
    publisher: {
      id: 'pub-1',
      email: '<EMAIL>',
      username: 'publisher1'
    },
    status: 'sp_approved' as const,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'app-2',
    campaign: {
      id: 'camp-2',
      title: 'Winter Campaign',
      advertiser: { id: 'adv-1', email: '<EMAIL>' }
    },
    publisher: {
      id: 'pub-2',
      email: '<EMAIL>',
      username: 'publisher2'
    },
    status: 'approved' as const,
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: 'app-3',
    campaign: {
      id: 'camp-1',
      title: 'Summer Campaign',
      advertiser: { id: 'adv-1', email: '<EMAIL>' }
    },
    publisher: {
      id: 'pub-3',
      email: '<EMAIL>',
      username: 'publisher3'
    },
    status: 'rejected' as const,
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z'
  }
];

const mockFilters = {
  status: 'sp_approved',
  search: '',
  campaignId: 'all'
};

const mockOnFilterChange = jest.fn();

describe('AdvertiserApplicationFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders status summary cards correctly', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    // Check status cards
    expect(screen.getByText('Total')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // Total count
    expect(screen.getByText('Awaiting Review')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // SP approved count
    expect(screen.getByText('Approved')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Approved count
    expect(screen.getByText('Rejected')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Rejected count
  });

  it('renders filter controls', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    // Check filter controls
    expect(screen.getByLabelText('Search applications by publisher email or campaign title')).toBeInTheDocument();
    expect(screen.getByLabelText('Filter applications by status')).toBeInTheDocument();
    expect(screen.getByLabelText('Filter applications by campaign')).toBeInTheDocument();
  });

  it('calls onFilterChange when search input changes', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    const searchInput = screen.getByLabelText('Search applications by publisher email or campaign title');
    fireEvent.change(searchInput, { target: { value: 'publisher1' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ search: 'publisher1' });
  });

  it('calls onFilterChange when status filter changes', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    const statusSelect = screen.getByLabelText('Filter applications by status');
    fireEvent.change(statusSelect, { target: { value: 'approved' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ status: 'approved' });
  });

  it('calls onFilterChange when campaign filter changes', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    const campaignSelect = screen.getByLabelText('Filter applications by campaign');
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ campaignId: 'camp-1' });
  });

  it('shows unique campaigns in campaign filter dropdown', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    const campaignSelect = screen.getByLabelText('Filter applications by campaign');
    expect(campaignSelect).toHaveTextContent('Summer Campaign');
    expect(campaignSelect).toHaveTextContent('Winter Campaign');
  });

  it('displays filtered count correctly', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={2}
      />
    );

    expect(screen.getByText('Showing 2 of 3 applications')).toBeInTheDocument();
  });

  it('shows clear filters button when filters are active', () => {
    const activeFilters = {
      status: 'approved',
      search: 'test',
      campaignId: 'camp-1'
    };

    render(
      <AdvertiserApplicationFilters
        filters={activeFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={1}
      />
    );

    const clearButton = screen.getByText('Clear Filters');
    expect(clearButton).toBeInTheDocument();
    expect(clearButton).not.toBeDisabled();
  });

  it('disables clear filters button when no active filters', () => {
    render(
      <AdvertiserApplicationFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={3}
      />
    );

    const clearButton = screen.getByText('Clear Filters');
    expect(clearButton).toBeDisabled();
  });

  it('calls onFilterChange with default values when clear filters is clicked', () => {
    const activeFilters = {
      status: 'approved',
      search: 'test',
      campaignId: 'camp-1'
    };

    render(
      <AdvertiserApplicationFilters
        filters={activeFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={1}
      />
    );

    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);

    expect(mockOnFilterChange).toHaveBeenCalledWith({
      status: 'sp_approved',
      search: '',
      campaignId: 'all'
    });
  });

  it('shows active filters display when filters are applied', () => {
    const activeFilters = {
      status: 'approved',
      search: 'publisher1',
      campaignId: 'camp-1'
    };

    render(
      <AdvertiserApplicationFilters
        filters={activeFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={1}
      />
    );

    expect(screen.getByText('Active filters:')).toBeInTheDocument();
    expect(screen.getByText('Status: Approved')).toBeInTheDocument();
    expect(screen.getByText('Search: "publisher1"')).toBeInTheDocument();
    expect(screen.getByText('Campaign: Summer Campaign')).toBeInTheDocument();
  });

  it('allows removing individual active filters', () => {
    const activeFilters = {
      status: 'approved',
      search: 'publisher1',
      campaignId: 'camp-1'
    };

    render(
      <AdvertiserApplicationFilters
        filters={activeFilters}
        onFilterChange={mockOnFilterChange}
        applications={mockApplications}
        filteredCount={1}
      />
    );

    // Remove status filter
    const statusRemoveButton = screen.getByLabelText('Remove status filter');
    fireEvent.click(statusRemoveButton);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ status: 'sp_approved' });

    // Remove search filter
    const searchRemoveButton = screen.getByLabelText('Remove search filter');
    fireEvent.click(searchRemoveButton);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ search: '' });

    // Remove campaign filter
    const campaignRemoveButton = screen.getByLabelText('Remove campaign filter');
    fireEvent.click(campaignRemoveButton);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ campaignId: 'all' });
  });
});
