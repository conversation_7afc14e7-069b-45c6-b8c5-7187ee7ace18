import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VideoReviewActions from '@/components/video-review/VideoReviewActions';

const mockOnApprove = jest.fn();
const mockOnReject = jest.fn();

describe('VideoReviewActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders approve and reject buttons for reviewable submissions', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    expect(screen.getByText('Approve')).toBeInTheDocument();
    expect(screen.getByText('Reject')).toBeInTheDocument();
  });

  it('shows status badge for non-reviewable submissions', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="approved"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    expect(screen.getByText('Approved')).toBeInTheDocument();
    expect(screen.queryByText('Approve')).not.toBeInTheDocument();
    expect(screen.queryByText('Reject')).not.toBeInTheDocument();
  });

  it('handles approve action with feedback modal', async () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Approve'));

    await waitFor(() => {
      expect(screen.getByText('Approve Video')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Approve Video');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnApprove).toHaveBeenCalledWith('test-submission', undefined);
    });
  });

  it('handles reject action with required feedback', async () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Reject'));

    await waitFor(() => {
      expect(screen.getByText('Reject Video')).toBeInTheDocument();
    });

    const feedbackTextarea = screen.getByLabelText('Rejection Reason (Required)');
    fireEvent.change(feedbackTextarea, { target: { value: 'Test rejection reason' } });

    const confirmButton = screen.getByText('Reject Video');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnReject).toHaveBeenCalledWith('test-submission', 'Test rejection reason');
    });
  });

  it('prevents rejection without feedback', async () => {
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Reject'));

    await waitFor(() => {
      expect(screen.getByText('Reject Video')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Reject Video');
    fireEvent.click(confirmButton);

    expect(alertSpy).toHaveBeenCalledWith('Please provide feedback for rejection.');
    expect(mockOnReject).not.toHaveBeenCalled();

    alertSpy.mockRestore();
  });

  it('shows correct permissions for SP Team role', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="sp_review"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    expect(screen.getByText('Approving will send to advertiser for final review')).toBeInTheDocument();
  });

  it('shows correct permissions for Advertiser role', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="advertiser_review"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="advertiser"
      />
    );

    expect(screen.getByText('Approving will mark the video as approved for publication')).toBeInTheDocument();
  });

  it('disables actions when disabled prop is true', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
        disabled={true}
      />
    );

    expect(screen.getByText('Approve')).toBeDisabled();
    expect(screen.getByText('Reject')).toBeDisabled();
  });

  it('handles API errors gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
    
    mockOnApprove.mockRejectedValue(new Error('API Error'));

    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Approve'));

    await waitFor(() => {
      expect(screen.getByText('Approve Video')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Approve Video');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith('Failed to approve submission. Please try again.');
    });

    consoleErrorSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('closes modal when cancel is clicked', async () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Approve'));

    await waitFor(() => {
      expect(screen.getByText('Approve Video')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Cancel'));

    await waitFor(() => {
      expect(screen.queryByText('Approve Video')).not.toBeInTheDocument();
    });
  });

  it('shows loading state during submission', async () => {
    mockOnApprove.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="staff"
      />
    );

    fireEvent.click(screen.getByText('Approve'));

    await waitFor(() => {
      expect(screen.getByText('Approve Video')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Approve Video');
    fireEvent.click(confirmButton);

    expect(screen.getByText('Processing...')).toBeInTheDocument();

    await waitFor(() => {
      expect(mockOnApprove).toHaveBeenCalled();
    });
  });

  it('restricts advertiser actions to advertiser_review status only', () => {
    render(
      <VideoReviewActions
        submissionId="test-submission"
        currentStatus="pending"
        onApprove={mockOnApprove}
        onReject={mockOnReject}
        userRole="advertiser"
      />
    );

    expect(screen.queryByText('Approve')).not.toBeInTheDocument();
    expect(screen.queryByText('Reject')).not.toBeInTheDocument();
    expect(screen.getByText('Waiting for SP Team review')).toBeInTheDocument();
  });
});
