import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VideoSubmissionForm from '@/components/publisher/VideoSubmissionForm';

// Mock campaigns data
const mockCampaigns = [
  {
    id: 'camp-1',
    title: 'Summer Fashion Campaign',
    description: 'Showcase summer fashion trends',
    status: 'active' as const,
    applicationStatus: 'approved' as const
  },
  {
    id: 'camp-2',
    title: 'Tech Product Launch',
    description: 'Promote new tech product',
    status: 'active' as const,
    applicationStatus: 'approved' as const
  }
];

const mockOnSubmit = jest.fn();

describe('VideoSubmissionForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders campaign selection dropdown', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByLabelText(/Select Campaign/)).toBeInTheDocument();
    expect(screen.getByText('Choose a campaign...')).toBeInTheDocument();
    expect(screen.getByText('Summer Fashion Campaign')).toBeInTheDocument();
    expect(screen.getByText('Tech Product Launch')).toBeInTheDocument();
  });

  it('shows message when no campaigns are available', () => {
    render(
      <VideoSubmissionForm
        campaigns={[]}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('No approved campaigns available. Apply to campaigns first.')).toBeInTheDocument();
  });

  it('shows campaign details when campaign is selected', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    expect(screen.getByText('Summer Fashion Campaign')).toBeInTheDocument();
    expect(screen.getByText('Showcase summer fashion trends')).toBeInTheDocument();
  });

  it('shows submission type selection after campaign is selected', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    expect(screen.getByText('Submission Type')).toBeInTheDocument();
    expect(screen.getByText('Upload Video')).toBeInTheDocument();
    expect(screen.getByText('TikTok Link')).toBeInTheDocument();
  });

  it('switches between video upload and TikTok link submission types', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    // Default should be video upload
    const videoButton = screen.getByText('Upload Video').closest('button');
    const tiktokButton = screen.getByText('TikTok Link').closest('button');

    expect(videoButton).toHaveClass('border-primary');
    expect(tiktokButton).not.toHaveClass('border-primary');

    // Switch to TikTok
    fireEvent.click(tiktokButton!);

    expect(tiktokButton).toHaveClass('border-primary');
    expect(videoButton).not.toHaveClass('border-primary');
  });

  it('shows video upload interface when video type is selected', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    expect(screen.getByText('Upload Your Video')).toBeInTheDocument();
    expect(screen.getByText('Drag and drop your video file here, or click to browse')).toBeInTheDocument();
  });

  it('shows TikTok link interface when TikTok type is selected', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    const tiktokButton = screen.getByText('TikTok Link').closest('button');
    fireEvent.click(tiktokButton!);

    expect(screen.getByText('Submit TikTok Link')).toBeInTheDocument();
    expect(screen.getByLabelText('TikTok Video URL')).toBeInTheDocument();
  });

  it('disables submit button when form is invalid', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    // No campaign selected - submit should not be visible
    expect(screen.queryByText('Submit Video')).not.toBeInTheDocument();

    // Select campaign but no video/link
    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    const submitButton = screen.getByText('Submit Video');
    expect(submitButton).toBeDisabled();
  });

  it('shows confirmation modal when form is submitted', async () => {
    // Mock file for video upload
    const mockFile = new File(['video content'], 'test-video.mp4', { type: 'video/mp4' });

    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    // Select campaign
    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    // Mock video selection (this would normally be done through the VideoUploadComponent)
    // For testing purposes, we'll simulate the form being valid
    const submitButton = screen.getByText('Submit Video');
    
    // We need to simulate a valid form state
    // In a real scenario, this would involve interacting with the VideoUploadComponent
    // For now, let's test the TikTok submission path which is easier to test

    // Switch to TikTok submission
    const tiktokButton = screen.getByText('TikTok Link').closest('button');
    fireEvent.click(tiktokButton!);

    // Enter TikTok URL (this would normally trigger validation in TikTokLinkComponent)
    const urlInput = screen.getByLabelText('TikTok Video URL');
    fireEvent.change(urlInput, { target: { value: 'https://www.tiktok.com/@user/video/1234567890' } });

    // Submit form
    fireEvent.click(submitButton);

    // Should show confirmation modal
    await waitFor(() => {
      expect(screen.getByText('Confirm Video Submission')).toBeInTheDocument();
    });
  });

  it('calls onSubmit with correct data when confirmed', async () => {
    mockOnSubmit.mockResolvedValue(undefined);

    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    // Select campaign
    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    // Switch to TikTok submission
    const tiktokButton = screen.getByText('TikTok Link').closest('button');
    fireEvent.click(tiktokButton!);

    // Enter TikTok URL
    const urlInput = screen.getByLabelText('TikTok Video URL');
    fireEvent.change(urlInput, { target: { value: 'https://www.tiktok.com/@user/video/1234567890' } });

    // Submit form
    const submitButton = screen.getByText('Submit Video');
    fireEvent.click(submitButton);

    // Confirm submission
    await waitFor(() => {
      expect(screen.getByText('Confirm Video Submission')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Confirm Submit');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith('camp-1', {
        tiktokUrl: 'https://www.tiktok.com/@user/video/1234567890'
      });
    });
  });

  it('resets form when reset button is clicked', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    // Select campaign
    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    // Switch to TikTok submission
    const tiktokButton = screen.getByText('TikTok Link').closest('button');
    fireEvent.click(tiktokButton!);

    // Reset form
    const resetButton = screen.getByText('Reset Form');
    fireEvent.click(resetButton);

    // Form should be reset
    expect(campaignSelect).toHaveValue('');
    expect(screen.queryByText('Submit TikTok Link')).not.toBeInTheDocument();
  });

  it('handles submission errors gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
    
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));

    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
      />
    );

    // Select campaign and submit
    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    fireEvent.change(campaignSelect, { target: { value: 'camp-1' } });

    const tiktokButton = screen.getByText('TikTok Link').closest('button');
    fireEvent.click(tiktokButton!);

    const urlInput = screen.getByLabelText('TikTok Video URL');
    fireEvent.change(urlInput, { target: { value: 'https://www.tiktok.com/@user/video/1234567890' } });

    const submitButton = screen.getByText('Submit Video');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Confirm Video Submission')).toBeInTheDocument();
    });

    const confirmButton = screen.getByText('Confirm Submit');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith('Failed to submit video. Please try again.');
    });

    consoleErrorSpy.mockRestore();
    alertSpy.mockRestore();
  });

  it('disables form when disabled prop is true', () => {
    render(
      <VideoSubmissionForm
        campaigns={mockCampaigns}
        onSubmit={mockOnSubmit}
        disabled={true}
      />
    );

    const campaignSelect = screen.getByLabelText(/Select Campaign/);
    expect(campaignSelect).toBeDisabled();
  });
});
