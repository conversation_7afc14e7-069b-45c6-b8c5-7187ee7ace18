'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
import ApplicationList from '@/components/spteam/ApplicationList';
import ApplicationFilters from '@/components/spteam/ApplicationFilters';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  reviewer?: {
    id: string;
    email: string;
  };
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
}

const SPTeamApplicationsPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // State management
  const [allApplications, setAllApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    search: '',
    campaignId: 'all'
  });

  // Client-side filtering
  const filteredApplications = useMemo(() => {
    return allApplications.filter(application => {
      // Status filter
      if (filters.status !== 'all' && application.status !== filters.status) {
        return false;
      }

      // Search filter (publisher email, username, or campaign title)
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const publisherEmail = application.publisher.email.toLowerCase();
        const publisherUsername = application.publisher.username?.toLowerCase() || '';
        const campaignTitle = application.campaign.title.toLowerCase();

        if (!publisherEmail.includes(searchLower) &&
            !publisherUsername.includes(searchLower) &&
            !campaignTitle.includes(searchLower)) {
          return false;
        }
      }

      // Campaign filter
      if (filters.campaignId !== 'all' && application.campaign.id !== filters.campaignId) {
        return false;
      }

      return true;
    });
  }, [allApplications, filters]);

  // Role-based access control
  useEffect(() => {
    if (user && user.role !== 'spteam') {
      router.replace('/unauthorized');
    }
  }, [user, router]);

  // Fetch applications data
  const fetchApplications = async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll fetch all applications and filter on the frontend
      // In a real implementation, the backend should support filtering
      const response = await fetch(`http://localhost:3000/applications`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. SP Team role required.');
        }
        throw new Error(`Failed to fetch applications: ${response.statusText}`);
      }

      const data = await response.json();

      // Since the backend might not include relations, we'll create mock data for now
      // In a real implementation, the backend should include campaign and publisher data
      const applicationsWithMockData = Array.isArray(data) ? data.map((app: any, index: number) => ({
        id: app.id || `app-${index}`,
        campaign: {
          id: app.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: `adv-${index}`,
            email: `advertiser${index + 1}@example.com`
          }
        },
        publisher: {
          id: app.publisherId || `pub-${index}`,
          email: `publisher${index + 1}@example.com`,
          username: `publisher${index + 1}`
        },
        status: app.status || 'pending',
        feedback: app.feedback,
        createdAt: app.createdAt || new Date().toISOString(),
        updatedAt: app.updatedAt || new Date().toISOString(),
        reviewer: app.reviewerId ? {
          id: app.reviewerId,
          email: `<EMAIL>`
        } : undefined
      })) : [];

      setAllApplications(applicationsWithMockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch applications');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user?.role === 'spteam') {
      fetchApplications();
    }
  }, [user, filters]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle bulk application review
  const handleBulkReviewApplication = async (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => {
    try {
      // Process each application individually
      // In a real implementation, this should be a single bulk API call
      const promises = applicationIds.map(async (applicationId) => {
        const response = await fetch(`http://localhost:3000/applications/${applicationId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: action === 'approve' ? 'approved' : 'rejected',
            feedback: feedback || null,
            reviewerId: user?.id,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to ${action} application ${applicationId}`);
        }

        return applicationId;
      });

      await Promise.all(promises);

      // Update the local state optimistically
      setAllApplications(prev => prev.map(app =>
        applicationIds.includes(app.id)
          ? {
              ...app,
              status: action === 'approve' ? 'approved' : 'rejected',
              feedback: feedback || app.feedback,
              reviewer: user ? { id: user.id, email: user.email } : app.reviewer,
              updatedAt: new Date().toISOString()
            }
          : app
      ));

      console.log(`Bulk ${action} completed for ${applicationIds.length} applications`);
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to bulk ${action} applications`);
      // Re-fetch applications to ensure UI is in sync with server
      await fetchApplications();
    }
  };

  // Handle application review
  const handleReviewApplication = async (applicationId: string, action: 'approve' | 'reject', feedback?: string) => {
    try {
      // For now, we'll use the basic PATCH endpoint since the review endpoint might not exist yet
      // In a real implementation, this should be a dedicated review endpoint
      const response = await fetch(`http://localhost:3000/applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: action === 'approve' ? 'approved' : 'rejected',
          feedback: feedback || null,
          reviewerId: user?.id, // Add the current user as reviewer
        }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. SP Team role required.');
        }
        if (response.status === 404) {
          throw new Error('Application not found.');
        }
        throw new Error(`Failed to ${action} application: ${response.statusText}`);
      }

      // Update the local state optimistically
      setAllApplications(prev => prev.map(app =>
        app.id === applicationId
          ? {
              ...app,
              status: action === 'approve' ? 'approved' : 'rejected',
              feedback: feedback || app.feedback,
              reviewer: user ? { id: user.id, email: user.email } : app.reviewer,
              updatedAt: new Date().toISOString()
            }
          : app
      ));

      // Show success message
      console.log(`Application ${action}d successfully`);

      // Optionally refresh from server to ensure consistency
      // await fetchApplications();
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} application`);
      // Re-fetch applications to ensure UI is in sync with server
      await fetchApplications();
    }
  };

  // Render loading state
  if (loading) {
    return (
      <AuthGuard>
        <div className="panel">
          <LoadingSpinner />
        </div>
      </AuthGuard>
    );
  }

  // Render error state
  if (error) {
    return (
      <AuthGuard>
        <div className="panel">
          <ErrorMessage message={error} onRetry={fetchApplications} />
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="panel">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Application Review Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Review and manage publisher applications for campaigns
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                Showing {filteredApplications.length} of {allApplications.length} applications
              </span>
            </div>
          </div>

          {/* Filters */}
          <ApplicationFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            applications={allApplications}
            filteredCount={filteredApplications.length}
          />
        </div>

        {/* Applications List */}
        <div className="panel">
          <ApplicationList
            applications={filteredApplications}
            onReviewApplication={handleReviewApplication}
            onBulkReview={handleBulkReviewApplication}
            loading={loading}
          />
        </div>
      </div>
    </AuthGuard>
  );
};

export default SPTeamApplicationsPage;
