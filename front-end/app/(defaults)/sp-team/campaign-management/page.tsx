'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
import CampaignForm from '@/components/campaign-management/CampaignForm';
import CampaignList from '@/components/campaign-management/CampaignList';
import PublisherAssignment from '@/components/campaign-management/PublisherAssignment';
import CampaignAnalytics from '@/components/campaign-management/CampaignAnalytics';

interface Campaign {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
  publisherAssignments?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    assignedAt: string;
    isActive: boolean;
  }[];
  applications?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
    appliedAt: string;
  }[];
  videoSubmissions?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
    submittedAt: string;
  }[];
}

interface FilterState {
  status: string;
  search: string;
  advertiserId: string;
  dateRange: string;
}

const SPTeamCampaignManagementPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // State management
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'active' | 'draft' | 'completed'>('overview');
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    search: '',
    advertiserId: 'all',
    dateRange: 'all'
  });

  // View state
  const [currentView, setCurrentView] = useState<'list' | 'create' | 'edit' | 'details' | 'assign' | 'analytics'>('list');
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Client-side filtering for campaigns
  const filteredCampaigns = useMemo(() => {
    return campaigns.filter(campaign => {
      // Tab-based filtering
      if (selectedTab === 'active' && campaign.status !== 'active') return false;
      if (selectedTab === 'draft' && campaign.status !== 'draft') return false;
      if (selectedTab === 'completed' && !['completed', 'cancelled'].includes(campaign.status)) return false;

      // Status filter
      if (filters.status !== 'all' && campaign.status !== filters.status) return false;

      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const titleMatch = campaign.title.toLowerCase().includes(searchLower);
        const advertiserMatch = campaign.advertiser.name.toLowerCase().includes(searchLower);
        if (!titleMatch && !advertiserMatch) return false;
      }

      // Advertiser filter
      if (filters.advertiserId !== 'all' && campaign.advertiserId !== filters.advertiserId) return false;

      // Date range filter
      if (filters.dateRange !== 'all') {
        const now = new Date();
        const campaignDate = new Date(campaign.createdAt);
        const daysDiff = Math.floor((now.getTime() - campaignDate.getTime()) / (1000 * 60 * 60 * 24));

        if (filters.dateRange === 'week' && daysDiff > 7) return false;
        if (filters.dateRange === 'month' && daysDiff > 30) return false;
        if (filters.dateRange === 'quarter' && daysDiff > 90) return false;
      }

      return true;
    });
  }, [campaigns, selectedTab, filters]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Campaign CRUD handlers
  const handleCreateCampaign = () => {
    setSelectedCampaign(null);
    setCurrentView('create');
  };

  const handleEditCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setCurrentView('edit');
  };

  const handleViewDetails = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setCurrentView('details');
  };

  const handleAssignPublishers = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setCurrentView('assign');
  };

  const handleViewAnalytics = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setCurrentView('analytics');
  };

  const handleAssignmentComplete = (updatedCampaign: Campaign) => {
    setCampaigns(prev => prev.map(c =>
      c.id === updatedCampaign.id ? updatedCampaign : c
    ));
  };

  const handleDeleteCampaign = async (campaignId: string) => {
    try {
      const response = await fetch(`http://localhost:3000/campaign/${campaignId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete campaign: ${response.statusText}`);
      }

      // Remove campaign from state
      setCampaigns(prev => prev.filter(c => c.id !== campaignId));

      console.log('Campaign deleted successfully');
    } catch (err) {
      console.error('Campaign deletion failed:', err);
      alert('Failed to delete campaign. Please try again.');
    }
  };

  const handleFormSubmit = async (campaignData: Omit<Campaign, 'id'>) => {
    setFormLoading(true);
    try {
      const isEditing = currentView === 'edit' && selectedCampaign;
      const url = isEditing
        ? `http://localhost:3000/campaign/${selectedCampaign.id}`
        : 'http://localhost:3000/campaign';

      const method = isEditing ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(campaignData),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${isEditing ? 'update' : 'create'} campaign: ${response.statusText}`);
      }

      const savedCampaign = await response.json();

      // Add mock advertiser data (in real app, backend should provide this)
      const campaignWithAdvertiser = {
        ...savedCampaign,
        advertiser: {
          id: savedCampaign.advertiserId,
          name: `Advertiser ${savedCampaign.advertiserId}`,
          email: `<EMAIL>`
        }
      };

      if (isEditing) {
        setCampaigns(prev => prev.map(c =>
          c.id === selectedCampaign.id ? campaignWithAdvertiser : c
        ));
      } else {
        setCampaigns(prev => [campaignWithAdvertiser, ...prev]);
      }

      setCurrentView('list');
      setSelectedCampaign(null);

      console.log(`Campaign ${isEditing ? 'updated' : 'created'} successfully`);
    } catch (err) {
      console.error('Campaign form submission failed:', err);
      alert(`Failed to ${currentView === 'edit' ? 'update' : 'create'} campaign. Please try again.`);
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormCancel = () => {
    setCurrentView('list');
    setSelectedCampaign(null);
  };

  // Fetch campaigns for SP Team
  const fetchCampaigns = async () => {
    try {
      setError(null);

      const response = await fetch(`http://localhost:3000/campaign`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. SP Team role required.');
        }
        throw new Error(`Failed to fetch campaigns: ${response.statusText}`);
      }

      const data = await response.json();

      // Transform data to include mock relations (in real app, backend should provide this)
      const campaignsWithMockData = Array.isArray(data) ? data.map((campaign: any, index: number) => ({
        id: campaign.id || `camp-${index}`,
        title: campaign.title || `Campaign ${index + 1}`,
        description: campaign.description || 'Campaign description',
        advertiserId: campaign.advertiserId || `adv-${index}`,
        advertiser: {
          id: campaign.advertiserId || `adv-${index}`,
          name: `Advertiser ${index + 1}`,
          email: `advertiser${index + 1}@example.com`
        },
        startDate: campaign.startDate || new Date().toISOString(),
        endDate: campaign.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: campaign.status || 'draft',
        assignedPublisherIds: campaign.assignedPublisherIds || [],
        createdAt: campaign.createdAt || new Date().toISOString(),
        updatedAt: campaign.updatedAt || new Date().toISOString(),
        publisherAssignments: [],
        applications: [],
        videoSubmissions: []
      })) : [];

      setCampaigns(campaignsWithMockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch campaigns');
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user?.role === 'staff') {
      const fetchData = async () => {
        setLoading(true);
        await fetchCampaigns();
        setLoading(false);
      };

      fetchData();
    }
  }, [user]);

  // Redirect if not SP Team
  useEffect(() => {
    if (user && user.role !== 'staff') {
      router.push('/unauthorized');
    }
  }, [user, router]);

  if (!user || user.role !== 'staff') {
    return (
      <AuthGuard>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AuthGuard>
    );
  }

  if (loading) {
    return (
      <AuthGuard>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading campaigns...</span>
        </div>
      </AuthGuard>
    );
  }

  if (error) {
    return (
      <AuthGuard>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Error Loading Campaigns</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4 text-center max-w-md">{error}</p>
          <button
            onClick={() => {
              setLoading(true);
              fetchCampaigns().finally(() => setLoading(false));
            }}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      </AuthGuard>
    );
  }

  // Calculate campaign statistics
  const campaignStats = {
    total: campaigns.length,
    active: campaigns.filter(c => c.status === 'active').length,
    draft: campaigns.filter(c => c.status === 'draft').length,
    completed: campaigns.filter(c => ['completed', 'cancelled'].includes(c.status)).length,
    paused: campaigns.filter(c => c.status === 'paused').length
  };

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Campaign Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage campaigns, assign publishers, and track performance
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {filteredCampaigns.length} campaigns
            </span>
            <button
              onClick={handleCreateCampaign}
              className="btn btn-primary"
              disabled={loading}
            >
              Create Campaign
            </button>
          </div>
        </div>

        {/* Campaign Statistics */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total</p>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{campaignStats.total}</p>
              </div>
              <div className="text-blue-500 text-2xl">📊</div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Active</p>
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">{campaignStats.active}</p>
              </div>
              <div className="text-green-500 text-2xl">🚀</div>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Draft</p>
                <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{campaignStats.draft}</p>
              </div>
              <div className="text-yellow-500 text-2xl">📝</div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg border border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Paused</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{campaignStats.paused}</p>
              </div>
              <div className="text-gray-500 text-2xl">⏸️</div>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Completed</p>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{campaignStats.completed}</p>
              </div>
              <div className="text-purple-500 text-2xl">✅</div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'overview'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Overview ({campaigns.length})
            </button>
            <button
              onClick={() => setSelectedTab('active')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'active'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Active ({campaignStats.active})
            </button>
            <button
              onClick={() => setSelectedTab('draft')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'draft'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Draft ({campaignStats.draft})
            </button>
            <button
              onClick={() => setSelectedTab('completed')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'completed'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Completed ({campaignStats.completed})
            </button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {currentView === 'list' && (
            <CampaignList
              campaigns={filteredCampaigns}
              onEdit={handleEditCampaign}
              onDelete={handleDeleteCampaign}
              onViewDetails={handleViewDetails}
              onAssignPublishers={handleAssignPublishers}
              loading={loading}
            />
          )}

          {(currentView === 'create' || currentView === 'edit') && (
            <CampaignForm
              campaign={selectedCampaign || undefined}
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              isEditing={currentView === 'edit'}
              loading={formLoading}
            />
          )}

          {currentView === 'details' && selectedCampaign && (
            <div className="panel p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Campaign Details
                </h2>
                <button
                  onClick={() => setCurrentView('list')}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="text-center text-gray-500">
                <h3 className="text-lg font-medium mb-2">Campaign Details View</h3>
                <p>Detailed campaign view with analytics will be implemented in Phase 3</p>
                <p className="mt-2 text-sm">Campaign: {selectedCampaign.title}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </AuthGuard>
  );
};

export default SPTeamCampaignManagementPage;
