'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
// Using inline loading and error components since common ones don't exist yet
import AdvertiserApplicationFilters from '@/components/advertiser/AdvertiserApplicationFilters';
import AdvertiserApplicationList from '@/components/advertiser/AdvertiserApplicationList';
import useRealTimeAdvertiserApplications from '@/hooks/useRealTimeAdvertiserApplications';

interface Application {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
  feedback?: string;
  createdAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
}

const AdvertiserApplicationsPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // Real-time data fetching
  const {
    applications: allApplications,
    loading,
    error,
    refetch: fetchApplications,
    lastUpdated
  } = useRealTimeAdvertiserApplications(30000); // 30 second polling

  // State management
  const [filters, setFilters] = useState<FilterState>({
    status: 'sp_approved', // Default to SP Team approved applications
    search: '',
    campaignId: 'all'
  });

  // Client-side filtering for advertiser-specific applications
  const filteredApplications = useMemo(() => {
    return allApplications.filter(application => {
      // Only show applications for campaigns owned by this advertiser
      if (application.campaign.advertiser.id !== user?.id) {
        return false;
      }

      // Status filter
      if (filters.status !== 'all' && application.status !== filters.status) {
        return false;
      }

      // Search filter (publisher email, username, or campaign title)
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const publisherEmail = application.publisher.email.toLowerCase();
        const publisherUsername = application.publisher.username?.toLowerCase() || '';
        const campaignTitle = application.campaign.title.toLowerCase();

        if (!publisherEmail.includes(searchLower) &&
            !publisherUsername.includes(searchLower) &&
            !campaignTitle.includes(searchLower)) {
          return false;
        }
      }

      // Campaign filter
      if (filters.campaignId !== 'all' && application.campaign.id !== filters.campaignId) {
        return false;
      }

      return true;
    });
  }, [allApplications, filters, user?.id]);

  // Local state for optimistic updates
  const [localApplications, setLocalApplications] = useState<Application[]>([]);

  // Sync real-time data with local state
  useEffect(() => {
    setLocalApplications(allApplications);
  }, [allApplications]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle application review
  const handleReviewApplication = async (applicationId: string, action: 'approve' | 'reject', feedback?: string) => {
    try {
      const response = await fetch(`http://localhost:3000/applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: action === 'approve' ? 'approved' : 'rejected',
          feedback: feedback || null,
          advertiserReviewerId: user?.id,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} application`);
      }

      // Update local state optimistically
      setAllApplications(prev => prev.map(app =>
        app.id === applicationId
          ? {
              ...app,
              status: (action === 'approve' ? 'approved' : 'rejected') as 'approved' | 'rejected',
              feedback: feedback || app.feedback,
              advertiserReviewer: user ? { id: user.id, email: user.email } : app.advertiserReviewer,
              updatedAt: new Date().toISOString()
            }
          : app
      ));

      console.log(`Application ${action}d successfully`);
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} application`);
      await fetchApplications(); // Re-fetch on error
    }
  };

  // Handle bulk application review
  const handleBulkReviewApplications = async (applicationIds: string[], action: 'approve' | 'reject', feedback?: string) => {
    try {
      // Process all applications in parallel
      const promises = applicationIds.map(applicationId =>
        fetch(`http://localhost:3000/applications/${applicationId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: action === 'approve' ? 'approved' : 'rejected',
            feedback: feedback || null,
            advertiserReviewerId: user?.id,
          }),
        })
      );

      const responses = await Promise.all(promises);

      // Check if all requests were successful
      const failedRequests = responses.filter(response => !response.ok);
      if (failedRequests.length > 0) {
        throw new Error(`Failed to ${action} ${failedRequests.length} applications`);
      }

      // Update local state optimistically
      setAllApplications(prev => prev.map(app =>
        applicationIds.includes(app.id)
          ? {
              ...app,
              status: (action === 'approve' ? 'approved' : 'rejected') as 'approved' | 'rejected',
              feedback: feedback || app.feedback,
              advertiserReviewer: user ? { id: user.id, email: user.email } : app.advertiserReviewer,
              updatedAt: new Date().toISOString()
            }
          : app
      ));

      console.log(`${applicationIds.length} applications ${action}d successfully`);
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} applications`);
      await fetchApplications(); // Re-fetch on error
    }
  };

  // Redirect if not advertiser
  if (user && user.role !== 'advertiser') {
    router.push('/');
    return null;
  }

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Application Review
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Review and approve publisher applications for your campaigns
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              Showing {filteredApplications.length} of {allApplications.length} applications
            </span>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="panel p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading applications...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="panel p-6">
            <div className="text-center py-12">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Applications</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
              <button
                onClick={fetchApplications}
                className="btn btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Main Content */}
        {!loading && !error && (
          <>
            {/* Application Filters */}
            <AdvertiserApplicationFilters
              filters={filters}
              onFilterChange={handleFilterChange}
              applications={allApplications}
              filteredCount={filteredApplications.length}
            />

            {/* Application List */}
            <AdvertiserApplicationList
              applications={filteredApplications}
              onReviewApplication={handleReviewApplication}
              onBulkReviewApplications={handleBulkReviewApplications}
              loading={loading}
            />
          </>
        )}
      </div>
    </AuthGuard>
  );
};

export default AdvertiserApplicationsPage;
