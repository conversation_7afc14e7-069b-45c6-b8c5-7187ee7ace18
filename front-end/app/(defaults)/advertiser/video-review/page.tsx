'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
import VideoSubmissionCard from '@/components/video-review/VideoSubmissionCard';
import VideoReviewFilters from '@/components/video-review/VideoReviewFilters';
import BulkReviewActions from '@/components/video-review/BulkReviewActions';
import useRealTimeVideoReview from '@/hooks/useRealTimeVideoReview';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
  submissionType: string;
}

const AdvertiserVideoReviewPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // Real-time data fetching
  const {
    videoSubmissions,
    loading,
    error,
    refetch,
    lastUpdated
  } = useRealTimeVideoReview('advertiser', 30000); // 30 second polling

  // State management
  const [selectedTab, setSelectedTab] = useState<'pending' | 'reviewed'>('pending');
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    search: '',
    campaignId: 'all',
    submissionType: 'all'
  });
  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([]);

  // Client-side filtering for video submissions (only advertiser's campaigns)
  const filteredSubmissions = useMemo(() => {
    return videoSubmissions.filter(submission => {
      // Only show submissions for advertiser's campaigns
      if (submission.campaign.advertiser.id !== user?.id) {
        return false;
      }

      // Tab-based filtering
      if (selectedTab === 'pending') {
        if (submission.status !== 'advertiser_review') {
          return false;
        }
      } else if (selectedTab === 'reviewed') {
        if (!['approved', 'rejected', 'published'].includes(submission.status)) {
          return false;
        }
      }

      // Status filter
      if (filters.status !== 'all' && submission.status !== filters.status) {
        return false;
      }

      // Search filter (campaign title or publisher)
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const campaignTitle = submission.campaign.title.toLowerCase();
        const publisherEmail = submission.publisher.email.toLowerCase();

        if (!campaignTitle.includes(searchLower) && !publisherEmail.includes(searchLower)) {
          return false;
        }
      }

      // Campaign filter
      if (filters.campaignId !== 'all' && submission.campaign.id !== filters.campaignId) {
        return false;
      }

      // Submission type filter
      if (filters.submissionType !== 'all') {
        if (filters.submissionType === 'video' && !submission.videoUrl) {
          return false;
        }
        if (filters.submissionType === 'tiktok' && !submission.tiktokUrl) {
          return false;
        }
      }

      return true;
    });
  }, [videoSubmissions, selectedTab, filters, user?.id]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle selection
  const handleSelectAll = () => {
    const reviewableSubmissions = filteredSubmissions.filter(sub =>
      sub.status === 'advertiser_review'
    );
    setSelectedSubmissions(reviewableSubmissions.map(sub => sub.id));
  };

  const handleClearSelection = () => {
    setSelectedSubmissions([]);
  };

  const handleToggleSelection = (submissionId: string) => {
    setSelectedSubmissions(prev =>
      prev.includes(submissionId)
        ? prev.filter(id => id !== submissionId)
        : [...prev, submissionId]
    );
  };

  // Handle bulk actions
  const handleBulkApprove = async (submissionIds: string[], feedback?: string) => {
    try {
      await Promise.all(submissionIds.map(id =>
        handleVideoApprove(id, feedback)
      ));
    } catch (error) {
      throw error;
    }
  };

  const handleBulkReject = async (submissionIds: string[], feedback: string) => {
    try {
      await Promise.all(submissionIds.map(id =>
        handleVideoReject(id, feedback)
      ));
    } catch (error) {
      throw error;
    }
  };



  // Handle video approve action
  const handleVideoApprove = async (submissionId: string, feedback?: string) => {
    try {
      const response = await fetch(`http://localhost:3000/video-submission/review/${submissionId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          feedback,
          reviewerId: user?.id,
          reviewerType: 'advertiser'
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to approve video: ${response.statusText}`);
      }

      // Refresh submissions
      await refetch();

      console.log('Video approved successfully');
    } catch (err) {
      console.error('Video approval failed:', err);
    }
  };

  // Handle video reject action
  const handleVideoReject = async (submissionId: string, feedback: string) => {
    try {
      const response = await fetch(`http://localhost:3000/video-submission/review/${submissionId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          feedback,
          reviewerId: user?.id,
          reviewerType: 'advertiser'
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to reject video: ${response.statusText}`);
      }

      // Refresh submissions
      await refetch();

      console.log('Video rejected successfully');
    } catch (err) {
      console.error('Video rejection failed:', err);
    }
  };



  // Redirect if not Advertiser
  useEffect(() => {
    if (user && user.role !== 'advertiser') {
      router.push('/unauthorized');
    }
  }, [user, router]);

  if (!user || user.role !== 'advertiser') {
    return (
      <AuthGuard>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AuthGuard>
    );
  }

  if (loading) {
    return (
      <AuthGuard>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading video submissions...</span>
        </div>
      </AuthGuard>
    );
  }

  if (error) {
    return (
      <AuthGuard>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Error Loading Video Submissions</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4 text-center max-w-md">{error}</p>
          <button
            onClick={refetch}
            className="btn btn-primary"
          >
            Try Again
          </button>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Video Review Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Review and approve video submissions for your campaigns
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {filteredSubmissions.length} submissions
            </span>
            {lastUpdated && (
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedTab('pending')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'pending'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Pending Review ({videoSubmissions.filter(sub => sub.status === 'advertiser_review' && sub.campaign.advertiser.id === user?.id).length})
            </button>
            <button
              onClick={() => setSelectedTab('reviewed')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'reviewed'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              Reviewed ({videoSubmissions.filter(sub => ['approved', 'rejected', 'published'].includes(sub.status) && sub.campaign.advertiser.id === user?.id).length})
            </button>
          </nav>
        </div>

        {/* Filters */}
        <VideoReviewFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          submissions={videoSubmissions}
          filteredCount={filteredSubmissions.length}
          userRole="advertiser"
        />

        {/* Bulk Actions */}
        <BulkReviewActions
          selectedSubmissions={selectedSubmissions}
          submissions={filteredSubmissions}
          onBulkApprove={handleBulkApprove}
          onBulkReject={handleBulkReject}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
          userRole="advertiser"
          disabled={loading}
        />

        {/* Tab Content */}
        <div className="space-y-6">
          {selectedTab === 'pending' && (
            <div className="space-y-6">
              {filteredSubmissions.length === 0 ? (
                <div className="panel p-12">
                  <div className="text-center text-gray-500">
                    <div className="text-6xl mb-4">🎬</div>
                    <h3 className="text-lg font-medium mb-2">No Pending Submissions</h3>
                    <p>There are no video submissions waiting for your review.</p>
                  </div>
                </div>
              ) : (
                filteredSubmissions.map((submission) => (
                  <div key={submission.id} className="relative">
                    <div className="absolute top-4 right-4 z-10">
                      <input
                        type="checkbox"
                        checked={selectedSubmissions.includes(submission.id)}
                        onChange={() => handleToggleSelection(submission.id)}
                        className="form-checkbox h-5 w-5 text-primary"
                        disabled={submission.status !== 'advertiser_review'}
                      />
                    </div>
                    <VideoSubmissionCard
                      submission={submission}
                      onApprove={handleVideoApprove}
                      onReject={handleVideoReject}
                      userRole="advertiser"
                    />
                  </div>
                ))
              )}
            </div>
          )}

          {selectedTab === 'reviewed' && (
            <div className="space-y-6">
              {filteredSubmissions.length === 0 ? (
                <div className="panel p-12">
                  <div className="text-center text-gray-500">
                    <div className="text-6xl mb-4">📋</div>
                    <h3 className="text-lg font-medium mb-2">No Reviewed Submissions</h3>
                    <p>There are no reviewed submissions to display.</p>
                  </div>
                </div>
              ) : (
                filteredSubmissions.map((submission) => (
                  <VideoSubmissionCard
                    key={submission.id}
                    submission={submission}
                    onApprove={handleVideoApprove}
                    onReject={handleVideoReject}
                    userRole="advertiser"
                  />
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </AuthGuard>
  );
};

export default AdvertiserVideoReviewPage;
