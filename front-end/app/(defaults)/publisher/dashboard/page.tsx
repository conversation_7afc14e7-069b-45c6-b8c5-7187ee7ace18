"use client";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { selectUser, selectAuth } from "@/store/authSlice";
import { useRouter } from "next/navigation";
import Link from "next/link";

// UI/UX mới: có thể dùng lại các className, style, panel, v.v. của front-end-new

const PublisherDashboard = () => {
  const router = useRouter();
  const { token, user } = useSelector(selectAuth);
  const [tiktokConnected, setTiktokConnected] = useState(false);
  const [tiktokUserInfo, setTiktokUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tiktokVideos, setTiktokVideos] = useState<any[]>([]);
  const [loadingVideos, setLoadingVideos] = useState(false);

  // Check for TikTok OAuth callback
  useEffect(() => {
    if (!user) return;
    const url = new URL(window.location.href);
    const code = url.searchParams.get("code");
    const state = url.searchParams.get("state");
    if (code && state) {
      setLoading(true);
      fetch(`http://localhost:3000/tiktok/oauth/callback?code=${code}&state=${user.id}`)
        .then((res) => res.json())
        .then((data) => {
          if (data.success) {
            setTiktokConnected(true);
            fetchUserInfo();
          } else {
            setError(data.error || "Failed to connect TikTok");
          }
        })
        .catch(() => setError("Failed to connect TikTok"))
        .finally(() => setLoading(false));
      // Remove code/state from URL
      url.searchParams.delete("code");
      url.searchParams.delete("state");
      window.history.replaceState({}, document.title, url.pathname);
    } else {
      fetchUserInfo();
    }
    // eslint-disable-next-line
  }, [user]);

  const fetchUserInfo = async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const res = await fetch("http://localhost:3000/tiktok/user-info", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (res.ok) {
        const data = await res.json();
        setTiktokUserInfo(data);
        setTiktokConnected(true);
      } else {
        setTiktokConnected(false);
      }
    } catch {
      setTiktokConnected(false);
    } finally {
      setLoading(false);
    }
  };

  const handleConnectTiktok = async () => {
    if (!token) return;
    setLoading(true);
    setError(null);
    try {
      const res = await fetch("http://localhost:3000/tiktok/connect-url", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await res.json();
      if (data.url) {
        window.location.href = data.url;
      } else {
        setError("Could not get TikTok connect URL");
      }
    } catch {
      setError("Could not get TikTok connect URL");
    } finally {
      setLoading(false);
    }
  };

  const fetchVideos = async () => {
    if (!token) return;
    setLoadingVideos(true);
    setError(null);
    try {
      const res = await fetch("http://localhost:3000/tiktok/videos", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (res.ok) {
        const data = await res.json();
        setTiktokVideos(data.data?.videos || []);
      } else {
        setError("Could not fetch TikTok videos");
      }
    } catch {
      setError("Could not fetch TikTok videos");
    } finally {
      setLoadingVideos(false);
    }
  };

  // Example stat data (có thể fetch từ API nếu backend có)
  const stats = [
    {
      label: "Active Campaigns",
      value: 5,
      icon: <span className="text-blue-600">📂</span>,
    },
    {
      label: "Applications",
      value: 12,
      icon: <span className="text-green-600">📄</span>,
    },
    {
      label: "Content Submitted",
      value: 8,
      icon: <span className="text-purple-600">📤</span>,
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-1">Creator Dashboard</h2>
        <p className="text-gray-600 max-w-xl">
          Discover brand collaboration opportunities, manage your applications, and submit content for approved campaigns. Start earning from your influence today.
        </p>
      </div>

      {/* Stat Widgets */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
        {stats.map((stat) => (
          <div key={stat.label} className="panel flex flex-col items-center p-6">
            <div className="mb-2 text-3xl">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-gray-500">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Chart Widget (placeholder) */}
      <div className="panel mb-8 p-6 flex flex-col items-center">
        <div className="mb-2 font-semibold">Campaign Performance</div>
        <div className="flex items-center justify-center h-48">
          <span className="text-5xl text-blue-300">📊</span>
          <span className="ml-4 text-gray-400">Chart coming soon</span>
        </div>
      </div>

      {/* Action Cards */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-3 mb-8">
        <div className="panel p-6 flex flex-col">
          <div className="font-semibold mb-2">Available Campaigns</div>
          <p className="text-gray-600 mb-4">Browse and apply for brand collaboration opportunities.</p>
          <button
            className="w-full py-2 px-4 bg-blue-700 text-white rounded hover:bg-blue-800 font-medium transition"
            onClick={() => router.push("/publisher/campaign-list")}
          >
            Explore Campaigns
          </button>
        </div>
        <div className="panel p-6 flex flex-col">
          <div className="font-semibold mb-2">My Applications</div>
          <p className="text-gray-600 mb-4">Track your application status and manage approvals.</p>
          <button
            className="w-full py-2 px-4 border border-blue-700 text-blue-700 rounded hover:bg-blue-50 font-medium transition"
            onClick={() => router.push("/publisher/campaign-list")}
          >
            View Applications
          </button>
        </div>
        <div className="panel p-6 flex flex-col">
          <div className="font-semibold mb-2">Content Submission</div>
          <p className="text-gray-600 mb-4">Upload and submit content for approved campaigns.</p>
          <button
            className="w-full py-2 px-4 bg-purple-600 text-white rounded hover:bg-purple-700 font-medium transition"
            onClick={() => router.push("/publisher/video-submission")}
          >
            Submit Videos
          </button>
        </div>
      </div>

      {/* TikTok Connect Card */}
      <div className="panel p-6 mb-8">
        <div className="font-semibold mb-2">TikTok Integration</div>
        {loading ? (
          <p>Loading...</p>
        ) : tiktokConnected ? (
          <div>
            <p className="text-green-600 mb-2">TikTok Connected!</p>
            {tiktokUserInfo && (
              <div className="text-sm text-gray-700 mb-2">
                <div>
                  <b>TikTok User ID:</b> {tiktokUserInfo.data?.user?.open_id}
                </div>
                <div>
                  <b>Display Name:</b> {tiktokUserInfo.data?.user?.display_name}
                </div>
              </div>
            )}
            <button
              className="w-full py-2 px-4 border border-blue-700 text-blue-700 rounded hover:bg-blue-50 font-medium transition"
              onClick={fetchUserInfo}
            >
              Refresh TikTok Info
            </button>
          </div>
        ) : (
          <div>
            <p className="mb-2">Connect your TikTok account to enable campaign submissions and analytics.</p>
            <button
              className="w-full py-2 px-4 bg-pink-600 text-white rounded hover:bg-pink-700 font-medium transition"
              onClick={handleConnectTiktok}
              disabled={loading}
            >
              Connect TikTok
            </button>
            {error && <p className="text-red-600 mt-2">{error}</p>}
          </div>
        )}
        {tiktokConnected && (
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-lg">Your TikTok Videos</h3>
              <button
                className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                onClick={fetchVideos}
                disabled={loadingVideos}
              >
                {loadingVideos ? "Loading..." : "Refresh Videos"}
              </button>
            </div>
            {tiktokVideos.length === 0 && !loadingVideos && (
              <p className="text-gray-500">No videos found or not fetched yet.</p>
            )}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {tiktokVideos.map((video: any) => (
                <div key={video.id || video.video_id} className="border rounded p-2 bg-gray-50">
                  {video.cover_image_url && (
                    <img src={video.cover_image_url} alt="Video thumbnail" className="w-full h-40 object-cover rounded mb-2" />
                  )}
                  <div className="font-medium mb-1">{video.title || video.id || video.video_id}</div>
                  <div className="text-xs text-gray-600">Views: {video.stats?.play_count ?? "N/A"}</div>
                  <div className="text-xs text-gray-600">Likes: {video.stats?.like_count ?? "N/A"}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublisherDashboard;
