'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
import VideoSubmissionForm from '@/components/publisher/VideoSubmissionForm';
import VideoSubmissionList from '@/components/publisher/VideoSubmissionList';
import VideoSubmissionFilters from '@/components/publisher/VideoSubmissionFilters';
import useRealTimeVideoSubmissions from '@/hooks/useRealTimeVideoSubmissions';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  applicationStatus?: 'pending' | 'sp_approved' | 'approved' | 'rejected';
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
  submissionType: string;
}

const PublisherVideoSubmissionPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // Real-time data fetching
  const {
    videoSubmissions,
    availableCampaigns,
    loading,
    error,
    refetch,
    lastUpdated
  } = useRealTimeVideoSubmissions(30000); // 30 second polling

  // State management
  const [selectedTab, setSelectedTab] = useState<'submit' | 'manage'>('submit');
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    search: '',
    campaignId: 'all',
    submissionType: 'all'
  });

  // Client-side filtering for video submissions
  const filteredSubmissions = useMemo(() => {
    return localSubmissions.filter(submission => {
      // Status filter
      if (filters.status !== 'all' && submission.status !== filters.status) {
        return false;
      }

      // Search filter (campaign title)
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const campaignTitle = submission.campaign.title.toLowerCase();

        if (!campaignTitle.includes(searchLower)) {
          return false;
        }
      }

      // Campaign filter
      if (filters.campaignId !== 'all' && submission.campaign.id !== filters.campaignId) {
        return false;
      }

      // Submission type filter
      if (filters.submissionType !== 'all') {
        if (filters.submissionType === 'video' && !submission.videoUrl) {
          return false;
        }
        if (filters.submissionType === 'tiktok' && !submission.tiktokUrl) {
          return false;
        }
      }

      return true;
    });
  }, [localSubmissions, filters]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Local state for optimistic updates
  const [localSubmissions, setLocalSubmissions] = useState<VideoSubmission[]>([]);

  // Sync real-time data with local state
  useEffect(() => {
    setLocalSubmissions(videoSubmissions);
  }, [videoSubmissions]);

  // Handle video submission
  const handleVideoSubmission = async (campaignId: string, submissionData: { videoFile?: File; tiktokUrl?: string }) => {
    try {
      const formData = new FormData();
      formData.append('campaignId', campaignId);
      formData.append('publisherId', user?.id || '');

      if (submissionData.videoFile) {
        formData.append('video', submissionData.videoFile);
      }

      if (submissionData.tiktokUrl) {
        formData.append('tiktokUrl', submissionData.tiktokUrl);
      }

      const response = await fetch(`http://localhost:3000/video-submission/submit`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to submit video: ${response.statusText}`);
      }

      // Add optimistic update
      const newSubmission: VideoSubmission = {
        id: `temp-${Date.now()}`,
        campaign: availableCampaigns.find(c => c.id === campaignId) || {
          id: campaignId,
          title: 'Unknown Campaign',
          advertiser: { id: 'unknown', email: '<EMAIL>' }
        },
        publisher: {
          id: user?.id || 'unknown',
          email: user?.email || '<EMAIL>',
          username: user?.username
        },
        videoUrl: submissionData.videoFile ? 'uploading...' : undefined,
        tiktokUrl: submissionData.tiktokUrl,
        status: 'pending',
        submittedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      setLocalSubmissions(prev => [newSubmission, ...prev]);

      // Refresh submissions from server
      await refetch();

      console.log('Video submitted successfully');
    } catch (err) {
      console.error('Video submission failed:', err);
      // Revert optimistic update on error
      setLocalSubmissions(videoSubmissions);
      throw err; // Re-throw to be handled by the form component
    }
  };

  // Handle resubmission
  const handleResubmit = (submissionId: string) => {
    // Find the submission and switch to submit tab with pre-filled data
    const submission = videoSubmissions.find(sub => sub.id === submissionId);
    if (submission) {
      setSelectedTab('submit');
      // In a real app, you might want to pre-fill the form with the submission data
      console.log('Resubmitting:', submission);
    }
  };

  // Handle view details
  const handleViewDetails = (submission: VideoSubmission) => {
    console.log('Viewing details for:', submission);
  };

  // Redirect if not publisher
  if (user && user.role !== 'publisher') {
    router.push('/');
    return null;
  }

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Video Submission
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Submit videos for your approved campaigns and track their review status
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {localSubmissions.length} submissions
            </span>
            {lastUpdated && (
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedTab('submit')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'submit'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Submit New Video
            </button>
            <button
              onClick={() => setSelectedTab('manage')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'manage'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Manage Submissions ({videoSubmissions.length})
            </button>
          </nav>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="panel p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading video submissions...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="panel p-6">
            <div className="text-center py-12">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Data</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
              <button
                onClick={refetch}
                className="btn btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Main Content */}
        {!loading && !error && (
          <>
            {selectedTab === 'submit' && (
              <div className="panel p-6">
                <VideoSubmissionForm
                  campaigns={availableCampaigns}
                  onSubmit={handleVideoSubmission}
                  disabled={loading}
                />
              </div>
            )}

            {selectedTab === 'manage' && (
              <>
                {/* Submission Filters */}
                <VideoSubmissionFilters
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  submissions={localSubmissions}
                  filteredCount={filteredSubmissions.length}
                />

                {/* Submission List */}
                <VideoSubmissionList
                  submissions={filteredSubmissions}
                  onResubmit={handleResubmit}
                  onViewDetails={handleViewDetails}
                  loading={loading}
                />
              </>
            )}
          </>
        )}
      </div>
    </AuthGuard>
  );
};

export default PublisherVideoSubmissionPage;
