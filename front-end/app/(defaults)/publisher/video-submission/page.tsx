'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/layouts/AuthGuard';
import VideoSubmissionForm from '@/components/publisher/VideoSubmissionForm';
import VideoSubmissionList from '@/components/publisher/VideoSubmissionList';
import VideoSubmissionFilters from '@/components/publisher/VideoSubmissionFilters';

interface VideoSubmission {
  id: string;
  campaign: {
    id: string;
    title: string;
    advertiser: {
      id: string;
      email: string;
    };
  };
  publisher: {
    id: string;
    email: string;
    username?: string;
  };
  videoUrl?: string;
  tiktokUrl?: string;
  status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
  feedback?: string;
  submittedAt: string;
  updatedAt: string;
  spReviewer?: {
    id: string;
    email: string;
  };
  advertiserReviewer?: {
    id: string;
    email: string;
  };
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'cancelled';
  applicationStatus?: 'pending' | 'sp_approved' | 'approved' | 'rejected';
}

interface FilterState {
  status: string;
  search: string;
  campaignId: string;
  submissionType: string;
}

const PublisherVideoSubmissionPage: React.FC = () => {
  const user = useSelector(selectUser);
  const router = useRouter();

  // State management
  const [videoSubmissions, setVideoSubmissions] = useState<VideoSubmission[]>([]);
  const [availableCampaigns, setAvailableCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<'submit' | 'manage'>('submit');
  const [filters, setFilters] = useState<FilterState>({
    status: 'all',
    search: '',
    campaignId: 'all',
    submissionType: 'all'
  });

  // Fetch video submissions
  const fetchVideoSubmissions = async () => {
    try {
      setError(null);

      const response = await fetch(`http://localhost:3000/video-submission`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. Publisher role required.');
        }
        throw new Error(`Failed to fetch video submissions: ${response.statusText}`);
      }

      const data = await response.json();

      // Transform data to include mock relations (in real app, backend should provide this)
      const submissionsWithMockData = Array.isArray(data) ? data.map((submission: any, index: number) => ({
        id: submission.id || `sub-${index}`,
        campaign: {
          id: submission.campaignId || `camp-${index}`,
          title: `Campaign ${index + 1}`,
          advertiser: {
            id: `adv-${index}`,
            email: `advertiser${index + 1}@example.com`
          }
        },
        publisher: {
          id: user?.id || `pub-${index}`,
          email: user?.email || `publisher${index + 1}@example.com`,
          username: user?.username || `publisher${index + 1}`
        },
        videoUrl: submission.videoUrl,
        tiktokUrl: submission.tiktokUrl,
        status: submission.status || 'pending',
        feedback: submission.feedback,
        submittedAt: submission.submittedAt || new Date().toISOString(),
        updatedAt: submission.updatedAt || new Date().toISOString(),
        spReviewer: submission.spReviewerId ? {
          id: submission.spReviewerId,
          email: `<EMAIL>`
        } : undefined,
        advertiserReviewer: submission.advertiserReviewerId ? {
          id: submission.advertiserReviewerId,
          email: `<EMAIL>`
        } : undefined
      })) : [];

      setVideoSubmissions(submissionsWithMockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch video submissions');
    }
  };

  // Fetch available campaigns (approved applications)
  const fetchAvailableCampaigns = async () => {
    try {
      // Mock data for now - in real app, fetch campaigns where user has approved applications
      const mockCampaigns: Campaign[] = [
        {
          id: 'camp-1',
          title: 'Summer Fashion Campaign',
          description: 'Showcase summer fashion trends',
          status: 'active',
          applicationStatus: 'approved'
        },
        {
          id: 'camp-2',
          title: 'Tech Product Launch',
          description: 'Promote new tech product',
          status: 'active',
          applicationStatus: 'approved'
        }
      ];

      setAvailableCampaigns(mockCampaigns);
    } catch (err) {
      console.error('Failed to fetch available campaigns:', err);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user?.role === 'publisher') {
      const fetchData = async () => {
        setLoading(true);
        await Promise.all([
          fetchVideoSubmissions(),
          fetchAvailableCampaigns()
        ]);
        setLoading(false);
      };

      fetchData();
    }
  }, [user]);

  // Handle video submission
  const handleVideoSubmission = async (campaignId: string, submissionData: { videoFile?: File; tiktokUrl?: string }) => {
    try {
      const formData = new FormData();
      formData.append('campaignId', campaignId);
      formData.append('publisherId', user?.id || '');

      if (submissionData.videoFile) {
        formData.append('video', submissionData.videoFile);
      }

      if (submissionData.tiktokUrl) {
        formData.append('tiktokUrl', submissionData.tiktokUrl);
      }

      const response = await fetch(`http://localhost:3000/video-submission/submit`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to submit video: ${response.statusText}`);
      }

      // Refresh submissions
      await fetchVideoSubmissions();

      console.log('Video submitted successfully');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit video');
    }
  };

  // Redirect if not publisher
  if (user && user.role !== 'publisher') {
    router.push('/');
    return null;
  }

  return (
    <AuthGuard>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Video Submission
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Submit videos for your approved campaigns and track their review status
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {videoSubmissions.length} submissions
            </span>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setSelectedTab('submit')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'submit'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Submit New Video
            </button>
            <button
              onClick={() => setSelectedTab('manage')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === 'manage'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Manage Submissions ({videoSubmissions.length})
            </button>
          </nav>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="panel p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading video submissions...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="panel p-6">
            <div className="text-center py-12">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Data</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
              <button
                onClick={() => {
                  setLoading(true);
                  fetchVideoSubmissions().finally(() => setLoading(false));
                }}
                className="btn btn-primary"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        {/* Main Content */}
        {!loading && !error && (
          <>
            {selectedTab === 'submit' && (
              <div className="panel p-6">
                <VideoSubmissionForm
                  campaigns={availableCampaigns}
                  onSubmit={handleVideoSubmission}
                  disabled={loading}
                />
              </div>
            )}

            {selectedTab === 'manage' && (
              <div className="panel p-6">
                <div className="text-center text-gray-500">
                  <h3 className="text-lg font-medium mb-2">Submission Management</h3>
                  <p>Video submission status tracking and management will be implemented in Phase 3</p>
                  <div className="mt-4 text-sm">
                    <p>Current submissions: {videoSubmissions.length}</p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AuthGuard>
  );
};

export default PublisherVideoSubmissionPage;
