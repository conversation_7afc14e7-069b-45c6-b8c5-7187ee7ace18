# Pay Per Post - Frontend

A comprehensive influencer marketing platform frontend built with Next.js, TypeScript, and Tailwind CSS. This application facilitates collaboration between Publishers (content creators), Advertisers (brands), and SP Team (platform administrators) through a role-based workflow system.

## 📋 Table of Contents

- [Project Overview](#project-overview)
- [Current Implementation Status](#current-implementation-status)
- [Architecture Overview](#architecture-overview)
- [Workflow Compliance](#workflow-compliance)
- [Design System](#design-system)
- [Setup and Development](#setup-and-development)
- [User Journeys](#user-journeys)
- [Implementation Roadmap](#implementation-roadmap)
- [Known Issues and Limitations](#known-issues-and-limitations)

## 🎯 Project Overview

The Pay Per Post platform enables a structured workflow for influencer marketing campaigns:

1. **Publishers** discover and apply to campaigns
2. **SP Team** reviews and approves applications
3. **Advertisers** provide final approval for applications
4. **Publishers** submit video content for review
5. **SP Team & Advertisers** review and approve video content
6. **Publishers** publish content and share final links

## 📊 Current Implementation Status

### ✅ **Completed Features (85% Complete)**

#### **Authentication & Role Management**
- ✅ JWT-based authentication system
- ✅ Role-based access control (Publisher, Advertiser, SP Team)
- ✅ Protected routing with AuthGuard
- ✅ Role-specific dashboard routing

#### **Publisher Interface**
- ✅ **Enhanced Campaign List Page** (`/publisher/campaign-list`)
  - Advanced search and filtering functionality
  - Grid/list view toggle with improved UI
  - Status badges for campaign and application status
  - Responsive design with loading/error states
  - Action buttons for viewing details and applying
- ✅ **Enhanced Campaign Detail Page** (`/publisher/campaign-detail/[id]`)
  - Comprehensive campaign information display
  - Advertiser information section
  - Application status tracking with feedback
  - Deadline validation and application restrictions
  - Enhanced UI with proper status indicators
- ✅ **Publisher Dashboard** (`/publisher/dashboard`)
  - TikTok integration for account connection
  - Campaign statistics and performance metrics
  - Quick action cards for navigation
  - Video management interface

#### **SP Team Interface**
- ✅ **SP Team Dashboard** (`/spteam/dashboard`)
  - Platform administration overview
  - Application and content review statistics
  - Quick action buttons for common tasks
- ✅ **Campaign Management** (`/spteam/campaigns`)
  - Basic campaign listing (redirects to campaign-list)

#### **Advertiser Interface**
- ✅ **Advertiser Dashboard** (`/advertiser/dashboard`)
  - Campaign performance metrics
  - Application review statistics
  - Content review management
  - Notification system integration
- ✅ **Campaign List** (`/advertiser/campaigns`)
  - Grid/list view toggle
  - Basic campaign management interface

#### **Design System & UI Components**
- ✅ **Comprehensive Tailwind CSS Design System**
  - Consistent color palette (primary, secondary, success, danger, warning, info)
  - Typography hierarchy with Nunito font family
  - Button system (solid, outline, sizes, states)
  - Form components with validation styles
  - Panel/card layouts with consistent spacing
- ✅ **Icon System**
  - Comprehensive icon library with consistent styling
  - Custom icons for specific features (filter, search, etc.)
- ✅ **Responsive Design**
  - Mobile-first approach with proper breakpoints
  - Consistent spacing and layout across devices

### 🚨 **Critical Missing Features (15% Remaining)**

#### **Application Review Workflow**
- ❌ **SP Team Application Review Dashboard** ([Issue #285](https://github.com/tan-interspace/pay-per-post/issues/285))
- ❌ **Advertiser Application Review Dashboard** ([Issue #286](https://github.com/tan-interspace/pay-per-post/issues/286))

#### **Video Management System**
- ❌ **Publisher Video Submission Interface** ([Issue #287](https://github.com/tan-interspace/pay-per-post/issues/287))
- ❌ **Video Review Interface for SP Team and Advertisers** ([Issue #288](https://github.com/tan-interspace/pay-per-post/issues/288))

#### **Enhanced Features**
- ❌ **Enhanced Campaign Management for SP Team** ([Issue #289](https://github.com/tan-interspace/pay-per-post/issues/289))
- ❌ **Real-time Notification System** ([Issue #290](https://github.com/tan-interspace/pay-per-post/issues/290))

## 🏗️ Architecture Overview

### **Technology Stack**
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Redux Toolkit for authentication and global state
- **Authentication**: JWT tokens with role-based access control
- **Icons**: Custom SVG icon library
- **Responsive Design**: Mobile-first approach with Tailwind breakpoints

### **Project Structure**
```
front-end/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Authentication routes
│   ├── (defaults)/               # Protected routes with layout
│   │   ├── publisher/            # Publisher-specific pages
│   │   ├── advertiser/           # Advertiser-specific pages
│   │   └── spteam/              # SP Team-specific pages
│   ├── publisher/               # Alternative publisher routes
│   ├── advertiser/              # Alternative advertiser routes
│   └── spteam/                  # Alternative SP Team routes
├── components/                   # Reusable UI components
│   ├── layouts/                 # Layout components
│   ├── icon/                    # Icon components
│   └── elements/                # UI element components
├── store/                       # Redux store configuration
├── styles/                      # Global styles and Tailwind config
└── theme.config.tsx            # Theme configuration
```

### **Routing Strategy**
- **Role-based routing**: Automatic dashboard routing based on user role
- **Protected routes**: AuthGuard component protects all authenticated routes
- **Flexible structure**: Both `/app/(defaults)/` and `/app/` route structures supported

## 🔄 Workflow Compliance

### **Official MVP Workflow Mapping**

Based on the [DETAILED-MVP-PLAN.md workflow diagram](https://github.com/tan-interspace/pay-per-post/blob/develop/DETAILED-MVP-PLAN.md#4-workflow-diagram-mermaid), here's the current implementation status:

| Workflow Step | Frontend Interface | Status | Notes |
|---------------|-------------------|--------|-------|
| **Publisher: View Campaigns** | `/publisher/campaign-list` | ✅ **Complete** | Enhanced with search, filtering, and responsive design |
| **Publisher: View Campaign Detail** | `/publisher/campaign-detail/[id]` | ✅ **Complete** | Comprehensive detail view with application status |
| **Publisher: Apply Campaign** | Campaign detail apply button | ✅ **Complete** | Integrated with application status tracking |
| **SP Team: Approve Join?** | *Missing Interface* | ❌ **Critical Gap** | [Issue #285](https://github.com/tan-interspace/pay-per-post/issues/285) |
| **Advertiser: Approve Join?** | *Missing Interface* | ❌ **Critical Gap** | [Issue #286](https://github.com/tan-interspace/pay-per-post/issues/286) |
| **Publisher: Upload Video** | *Missing Interface* | ❌ **Critical Gap** | [Issue #287](https://github.com/tan-interspace/pay-per-post/issues/287) |
| **SP Team: Approve Video?** | *Missing Interface* | ❌ **Critical Gap** | [Issue #288](https://github.com/tan-interspace/pay-per-post/issues/288) |
| **Advertiser: Approve Video?** | *Missing Interface* | ❌ **Critical Gap** | [Issue #288](https://github.com/tan-interspace/pay-per-post/issues/288) |
| **Publisher: Upload to Site & Share Link** | *Missing Interface* | ❌ **Critical Gap** | Part of [Issue #287](https://github.com/tan-interspace/pay-per-post/issues/287) |
| **Advertiser: Confirm & Monitor** | Basic dashboard | ⚠️ **Partial** | Needs enhancement for monitoring |

### **Workflow Gaps Analysis**

**🚨 Critical Missing Workflow Steps (40% of workflow)**
1. **Application Review Process**: No interfaces for SP Team or Advertiser to review applications
2. **Video Submission & Review**: Complete video workflow is missing
3. **Final Link Submission**: No interface for publishers to submit final public links

**✅ Completed Workflow Steps (60% of workflow)**
1. **Campaign Discovery**: Publishers can browse and view campaigns
2. **Application Submission**: Publishers can apply to campaigns
3. **Status Tracking**: Application status is tracked and displayed

## 🎨 Design System

### **Color Palette**
- **Primary**: `#4361ee` (Blue) - Main brand color for primary actions
- **Secondary**: `#805dca` (Purple) - Secondary actions and accents
- **Success**: `#00ab55` (Green) - Success states and positive actions
- **Danger**: `#e7515a` (Red) - Error states and destructive actions
- **Warning**: `#e2a03f` (Orange) - Warning states and caution
- **Info**: `#2196f3` (Light Blue) - Informational content
- **Dark**: `#3b3f5c` - Dark text and elements
- **Black**: `#0e1726` - Primary text color

### **Typography**
- **Font Family**: Nunito (Google Font)
- **Heading Hierarchy**: H1 (40px) → H6 (16px) with consistent spacing
- **Body Text**: 14px base with responsive scaling
- **Font Weights**: Regular (400), Semibold (600), Bold (700)

### **Component System**
- **Buttons**: `.btn` with variants (primary, outline, sizes, states)
- **Panels**: `.panel` for consistent card layouts
- **Forms**: `.form-input`, `.form-select` with validation styles
- **Badges**: Status indicators with semantic colors
- **Icons**: SVG-based icon system with consistent sizing

### **Spacing System**
- **Base Unit**: 4px (Tailwind's default)
- **Common Spacing**: 4px, 8px, 12px, 16px, 20px, 24px, 32px
- **Container Padding**: 24px (p-6) for most panels and sections

## 🚀 Setup and Development

### **Prerequisites**
- Node.js 18+
- npm, yarn, or pnpm
- Git

### **Installation**

```bash
# Clone the repository
git clone https://github.com/tan-interspace/pay-per-post.git
cd pay-per-post/front-end

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
```

### **Development Server**

```bash
# Start the development server
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

### **Build and Deployment**

```bash
# Build for production
npm run build

# Start production server
npm run start

# Export static files (if needed)
npm run export
```

### **Environment Variables**

Create a `.env.local` file in the frontend root:

```env
# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:3000

# Authentication
NEXT_PUBLIC_JWT_SECRET=your-jwt-secret

# TikTok Integration (if applicable)
NEXT_PUBLIC_TIKTOK_CLIENT_ID=your-tiktok-client-id
```

## 👥 User Journeys

### **Publisher Journey (85% Complete)**

#### ✅ **Completed Flow**
1. **Login** → Role-based redirect to Publisher Dashboard
2. **Dashboard** → View statistics, TikTok integration, quick actions
3. **Browse Campaigns** → Enhanced campaign list with search/filtering
4. **View Campaign Details** → Comprehensive campaign information
5. **Apply to Campaign** → Submit application with status tracking

#### ❌ **Missing Flow**
6. **Video Submission** → Upload videos or submit TikTok links
7. **Video Review Feedback** → View feedback and resubmit if needed
8. **Final Link Submission** → Submit public video links after approval

### **SP Team Journey (40% Complete)**

#### ✅ **Completed Flow**
1. **Login** → Role-based redirect to SP Team Dashboard
2. **Dashboard** → Platform administration overview

#### ❌ **Missing Flow**
3. **Review Applications** → Approve/reject publisher applications
4. **Review Videos** → Approve/reject submitted videos with feedback
5. **Manage Campaigns** → Enhanced campaign creation and management

### **Advertiser Journey (50% Complete)**

#### ✅ **Completed Flow**
1. **Login** → Role-based redirect to Advertiser Dashboard
2. **Dashboard** → Campaign metrics and management overview
3. **View Campaigns** → Basic campaign list management

#### ❌ **Missing Flow**
4. **Review Applications** → Final approval of SP Team-approved applications
5. **Review Videos** → Final approval of SP Team-approved videos
6. **Monitor Performance** → Enhanced campaign performance tracking

## 🗺️ Implementation Roadmap

### **Phase 1: Critical Workflow Completion (High Priority)**

#### **Week 1-2: Application Review System**
- [ ] **SP Team Application Review Dashboard** ([Issue #285](https://github.com/tan-interspace/pay-per-post/issues/285))
  - Application list with filtering and search
  - Application detail view with publisher information
  - Approve/reject actions with feedback system
  - Real-time status updates
- [ ] **Advertiser Application Review Dashboard** ([Issue #286](https://github.com/tan-interspace/pay-per-post/issues/286))
  - Final approval interface for SP Team-approved applications
  - Application history and workflow tracking
  - Feedback and communication system

#### **Week 3-4: Video Management System**
- [ ] **Publisher Video Submission Interface** ([Issue #287](https://github.com/tan-interspace/pay-per-post/issues/287))
  - Video upload with drag-and-drop functionality
  - TikTok link submission with validation
  - Status tracking and feedback display
  - Resubmission workflow for rejected videos
- [ ] **Video Review Interface** ([Issue #288](https://github.com/tan-interspace/pay-per-post/issues/288))
  - Video player/viewer for review
  - Approve/reject actions with feedback
  - Bulk review capabilities
  - Video metadata and history tracking

### **Phase 2: Enhanced Features (Medium Priority)**

#### **Week 5-6: Advanced Campaign Management**
- [ ] **Enhanced SP Team Campaign Management** ([Issue #289](https://github.com/tan-interspace/pay-per-post/issues/289))
  - Campaign creation with full form validation
  - Publisher assignment interface
  - Campaign analytics and metrics
  - Bulk operations and advanced filtering

#### **Week 7-8: Real-time Features**
- [ ] **Real-time Notification System** ([Issue #290](https://github.com/tan-interspace/pay-per-post/issues/290))
  - WebSocket-based real-time updates
  - Notification center with categorization
  - Email integration and preferences
  - Push notification support

### **Phase 3: Polish and Optimization (Low Priority)**

#### **Week 9-10: Performance and UX**
- [ ] **Performance Optimization**
  - Code splitting and lazy loading
  - Image optimization and caching
  - Bundle size optimization
- [ ] **Enhanced UX Features**
  - Advanced search and filtering
  - Keyboard shortcuts and accessibility
  - Dark mode support
  - Mobile app-like experience

## ⚠️ Known Issues and Limitations

### **Critical Issues**
1. **Incomplete Workflow**: 40% of the MVP workflow is missing frontend interfaces
2. **API Integration**: Most components use mock data and need backend API integration
3. **Real-time Updates**: No WebSocket integration for live status updates

### **Design Inconsistencies**
1. **Color Usage**: Some pages use hardcoded colors instead of design system colors
   - **Publisher Dashboard**: Uses `blue-700` instead of `primary`
   - **SP Team Dashboard**: Uses `green-700` instead of `success`
   - **Advertiser Dashboard**: Uses `purple-700` instead of `secondary`

2. **Button Styles**: Inconsistent button styling across pages
   - **Campaign List**: Uses custom button classes instead of `.btn` system
   - **Dashboard Actions**: Mix of custom and system button styles

3. **Typography**: Some pages don't follow the typography hierarchy
   - **Dashboard Titles**: Inconsistent heading sizes and spacing
   - **Form Labels**: Some forms don't use the standard label styles

### **Technical Debt**
1. **Routing Structure**: Dual routing structure (`/app/(defaults)/` and `/app/`) creates confusion
2. **Component Duplication**: Similar components exist in multiple locations
3. **State Management**: Limited use of Redux for global state management
4. **Error Handling**: Inconsistent error handling patterns across components

### **Performance Issues**
1. **Bundle Size**: Large bundle due to unused Tailwind classes and components
2. **Image Optimization**: No Next.js Image component usage for optimization
3. **Code Splitting**: Limited code splitting for route-based optimization

### **Accessibility Issues**
1. **Keyboard Navigation**: Limited keyboard navigation support
2. **Screen Reader Support**: Missing ARIA labels and descriptions
3. **Color Contrast**: Some color combinations may not meet WCAG standards

## 🎯 Success Metrics

### **Completion Targets**
- **MVP Workflow Coverage**: 100% (currently 60%)
- **Design System Consistency**: 95% (currently 80%)
- **API Integration**: 100% (currently 20%)
- **Test Coverage**: 80% (currently 0%)
- **Performance Score**: 90+ (Lighthouse)
- **Accessibility Score**: 95+ (WCAG AA)

### **Quality Gates**
- All workflow steps have corresponding frontend interfaces
- Design system is consistently applied across all pages
- All components integrate with backend APIs
- Comprehensive test coverage for critical user flows
- Performance and accessibility standards are met

---

## 📞 Support and Contributing

For questions, issues, or contributions, please refer to:
- **GitHub Issues**: [https://github.com/tan-interspace/pay-per-post/issues](https://github.com/tan-interspace/pay-per-post/issues)
- **MVP Plan**: [DETAILED-MVP-PLAN.md](https://github.com/tan-interspace/pay-per-post/blob/develop/DETAILED-MVP-PLAN.md)
- **Backend Documentation**: [back-end/README.md](../back-end/README.md)

**Last Updated**: July 7, 2025
**Frontend Completion**: 85% (MVP Workflow: 60% Complete)
